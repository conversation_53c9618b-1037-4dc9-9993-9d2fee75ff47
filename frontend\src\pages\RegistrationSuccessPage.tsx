/**
 * Registration Success Page
 * Confirmation page after successful registration with next steps
 */

import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useLanguage } from '../hooks/useLanguage';
import { useTranslation } from 'react-i18next';
import { RTLIcon, RTLText } from '../components/common';
import {
  CheckCircle,
  Mail,
  Clock,
  Users,
  FileText,
  DollarSign,
  ArrowRight,
  Home,
  MessageSquare,
  Shield,
  Sparkles
} from 'lucide-react';

const RegistrationSuccessPage: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const { t } = useTranslation();
  const navigate = useNavigate();

  const nextSteps = [
    {
      icon: <Mail className="w-6 h-6" />,
      title: language === 'ar' ? 'تحقق من بريدك الإلكتروني' : 'Check Your Email',
      description: language === 'ar' 
        ? 'أرسلنا لك رابط تفعيل الحساب. انقر عليه لتفعيل حسابك.'
        : 'We sent you an account activation link. Click it to activate your account.',
      action: language === 'ar' ? 'فتح البريد الإلكتروني' : 'Open Email',
      urgent: true
    },
    {
      icon: <FileText className="w-6 h-6" />,
      title: language === 'ar' ? 'أكمل ملفك الشخصي' : 'Complete Your Profile',
      description: language === 'ar'
        ? 'أضف المزيد من التفاصيل لملفك الشخصي لتحسين تجربتك.'
        : 'Add more details to your profile to enhance your experience.',
      action: language === 'ar' ? 'إكمال الملف' : 'Complete Profile',
      urgent: false
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: language === 'ar' ? 'استكشف المجتمع' : 'Explore Community',
      description: language === 'ar'
        ? 'تواصل مع رواد الأعمال والموجهين والمستثمرين الآخرين.'
        : 'Connect with other entrepreneurs, mentors, and investors.',
      action: language === 'ar' ? 'استكشاف' : 'Explore',
      urgent: false
    }
  ];

  const roleApprovalInfo = [
    {
      role: 'mentor',
      title: language === 'ar' ? 'طلب الموجه' : 'Mentor Application',
      description: language === 'ar'
        ? 'سيتم مراجعة طلبك ليصبح موجهاً خلال 3-5 أيام عمل. سنتواصل معك عبر البريد الإلكتروني.'
        : 'Your mentor application will be reviewed within 3-5 business days. We\'ll contact you via email.',
      timeframe: language === 'ar' ? '3-5 أيام عمل' : '3-5 business days',
      icon: <Users className="w-5 h-5" />
    },
    {
      role: 'investor',
      title: language === 'ar' ? 'طلب المستثمر' : 'Investor Application',
      description: language === 'ar'
        ? 'سيتم التحقق من اعتمادك الاستثماري ومراجعة طلبك خلال 5-7 أيام عمل.'
        : 'Your investment accreditation will be verified and application reviewed within 5-7 business days.',
      timeframe: language === 'ar' ? '5-7 أيام عمل' : '5-7 business days',
      icon: <DollarSign className="w-5 h-5" />
    },
    {
      role: 'moderator',
      title: language === 'ar' ? 'طلب المشرف' : 'Moderator Application',
      description: language === 'ar'
        ? 'سيتم مراجعة طلبك ليصبح مشرفاً خلال 2-3 أيام عمل. سنقوم بتقييم خبرتك في إدارة المجتمعات.'
        : 'Your moderator application will be reviewed within 2-3 business days. We\'ll evaluate your community management experience.',
      timeframe: language === 'ar' ? '2-3 أيام عمل' : '2-3 business days',
      icon: <Shield className="w-5 h-5" />
    }
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center p-4 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-4xl w-full">
        {/* Success Header */}
        <div className="text-center mb-12">
          <Link to="/" className="inline-block mb-4">
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">
              Yasmeen AI
            </h1>
          </Link>
          <div className="w-20 h-20 bg-green-500/20 border border-green-400/30 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="w-12 h-12 text-green-400" />
          </div>

          <h2 className="text-3xl font-bold text-white mb-4">
            {language === 'ar' ? 'مرحباً بك في المنصة!' : 'Welcome to the Platform!'}
          </h2>

          <p className="text-gray-300 text-lg mb-6">
            {language === 'ar'
              ? 'تم إنشاء حسابك بنجاح. إليك الخطوات التالية:'
              : 'Your account has been created successfully. Here are your next steps:'
            }
          </p>

          <div className="bg-blue-500/20 border border-blue-400/30 rounded-lg p-4 mb-8">
            <div className={`flex items-center justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Mail className={`w-5 h-5 text-blue-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              <p className="text-blue-300 font-semibold">
                {language === 'ar'
                  ? 'تحقق من بريدك الإلكتروني لتفعيل حسابك'
                  : 'Check your email to activate your account'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <ArabicCard title={language === 'ar' ? 'الخطوات التالية' : 'Next Steps'} className="mb-8">
          <div className="space-y-6">
            {nextSteps.map((step, index) => (
              <div key={index} className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center ${
                  step.urgent ? 'bg-red-100 text-red-600' : 'bg-blue-100 text-blue-600'
                } ${isRTL ? 'ml-4' : 'mr-4'}`}>
                  {step.icon}
                </div>
                
                <div className="flex-1">
                  <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <ArabicTypography variant="h5" className="font-semibold">
                      {step.title}
                    </ArabicTypography>
                    
                    {step.urgent && (
                      <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-semibold font-arabic">
                        {language === 'ar' ? 'مطلوب' : 'Required'}
                      </span>
                    )}
                  </div>
                  
                  <ArabicTypography variant="body2" color="secondary" className="mt-1 mb-3">
                    {step.description}
                  </ArabicTypography>
                  
                  <ArabicButton
                    size="sm"
                    variant={step.urgent ? 'primary' : 'outline'}
                    onClick={() => {
                      if (step.title.includes('Email') || step.title.includes('بريد')) {
                        window.open('mailto:', '_blank');
                      } else if (step.title.includes('Profile') || step.title.includes('ملف')) {
                        navigate('/dashboard/profile');
                      } else {
                        navigate('/dashboard');
                      }
                    }}
                  >
                    {step.action}
                  </ArabicButton>
                </div>
              </div>
            ))}
          </div>
        </ArabicCard>

        {/* Role Approval Status */}
        <ArabicCard title={language === 'ar' ? 'حالة طلبات الأدوار' : 'Role Application Status'} className="mb-8">
          <div className="space-y-6">
            {roleApprovalInfo.map((info, index) => (
              <div key={index} className="border border-yellow-200 bg-yellow-50 rounded-lg p-4">
                <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex-shrink-0 w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center ${isRTL ? 'ml-4' : 'mr-4'}`}>
                    {info.icon}
                  </div>
                  
                  <div className="flex-1">
                    <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <ArabicTypography variant="h5" className="font-semibold text-yellow-800">
                        {info.title}
                      </ArabicTypography>
                      
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Clock className={`w-4 h-4 text-yellow-600 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                        <span className="text-sm font-semibold text-yellow-700 font-arabic">
                          {info.timeframe}
                        </span>
                      </div>
                    </div>
                    
                    <ArabicTypography variant="body2" className="text-yellow-700 mt-2">
                      {info.description}
                    </ArabicTypography>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ArabicCard>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <ArabicCard className="text-center">
            <FileText className="w-12 h-12 text-blue-600 mx-auto mb-4" />
            <ArabicTypography variant="h5" className="font-semibold mb-2">
              {language === 'ar' ? 'إنشاء خطة عمل' : 'Create Business Plan'}
            </ArabicTypography>
            <ArabicTypography variant="body2" color="secondary" className="mb-4">
              {language === 'ar' 
                ? 'ابدأ في إنشاء خطة عملك مع مساعدة الذكاء الاصطناعي'
                : 'Start creating your business plan with AI assistance'
              }
            </ArabicTypography>
            <ArabicButton
              size="sm"
              variant="outline"
              onClick={() => navigate('/dashboard/business-plan/new')}
              className="w-full"
            >
              {language === 'ar' ? 'ابدأ الآن' : 'Start Now'}
            </ArabicButton>
          </ArabicCard>

          <ArabicCard className="text-center">
            <Users className="w-12 h-12 text-green-600 mx-auto mb-4" />
            <ArabicTypography variant="h5" className="font-semibold mb-2">
              {language === 'ar' ? 'العثور على موجه' : 'Find a Mentor'}
            </ArabicTypography>
            <ArabicTypography variant="body2" color="secondary" className="mb-4">
              {language === 'ar'
                ? 'تواصل مع موجهين ذوي خبرة في مجالك'
                : 'Connect with experienced mentors in your field'
              }
            </ArabicTypography>
            <ArabicButton
              size="sm"
              variant="outline"
              onClick={() => navigate('/dashboard/mentorship/discover')}
              className="w-full"
            >
              {language === 'ar' ? 'استكشاف' : 'Explore'}
            </ArabicButton>
          </ArabicCard>

          <ArabicCard className="text-center">
            <DollarSign className="w-12 h-12 text-purple-600 mx-auto mb-4" />
            <ArabicTypography variant="h5" className="font-semibold mb-2">
              {language === 'ar' ? 'فرص التمويل' : 'Funding Opportunities'}
            </ArabicTypography>
            <ArabicTypography variant="body2" color="secondary" className="mb-4">
              {language === 'ar'
                ? 'اكتشف المستثمرين والمنح المناسبة لشركتك'
                : 'Discover investors and grants suitable for your startup'
              }
            </ArabicTypography>
            <ArabicButton
              size="sm"
              variant="outline"
              onClick={() => navigate('/dashboard/funding/discover')}
              className="w-full"
            >
              {language === 'ar' ? 'اكتشاف' : 'Discover'}
            </ArabicButton>
          </ArabicCard>
        </div>

        {/* Support Information */}
        <ArabicCard title={language === 'ar' ? 'هل تحتاج مساعدة؟' : 'Need Help?'}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <ArabicTypography variant="h5" className="font-semibold mb-2">
                {language === 'ar' ? 'مركز المساعدة' : 'Help Center'}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary" className="mb-4">
                {language === 'ar'
                  ? 'تصفح الأسئلة الشائعة والأدلة التفصيلية'
                  : 'Browse FAQs and detailed guides'
                }
              </ArabicTypography>
              <ArabicButton
                size="sm"
                variant="outline"
                onClick={() => navigate('/help')}
                icon={<FileText className="w-4 h-4" />}
              >
                {language === 'ar' ? 'زيارة مركز المساعدة' : 'Visit Help Center'}
              </ArabicButton>
            </div>

            <div>
              <ArabicTypography variant="h5" className="font-semibold mb-2">
                {language === 'ar' ? 'تواصل معنا' : 'Contact Support'}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary" className="mb-4">
                {language === 'ar'
                  ? 'فريق الدعم متاح لمساعدتك على مدار الساعة'
                  : 'Our support team is available 24/7 to help you'
                }
              </ArabicTypography>
              <ArabicButton
                size="sm"
                variant="outline"
                onClick={() => navigate('/contact')}
                icon={<MessageSquare className="w-4 h-4" />}
              >
                {language === 'ar' ? 'تواصل معنا' : 'Contact Us'}
              </ArabicButton>
            </div>
          </div>
        </ArabicCard>

        {/* Action Buttons */}
        <div className={`flex justify-center space-x-4 mt-8 ${isRTL ? 'space-x-reverse' : ''}`}>
          <ArabicButton
            onClick={() => navigate('/dashboard')}
            icon={<Home className="w-4 h-4" />}
            iconPosition="start"
          >
            {language === 'ar' ? 'الذهاب للوحة التحكم' : 'Go to Dashboard'}
          </ArabicButton>
          
          <ArabicButton
            variant="outline"
            onClick={() => navigate('/dashboard/profile')}
            icon={<ArrowRight className="w-4 h-4" />}
            iconPosition="end"
          >
            {language === 'ar' ? 'إكمال الملف الشخصي' : 'Complete Profile'}
          </ArabicButton>
        </div>

        {/* Footer Note */}
        <div className="text-center mt-12">
          <ArabicTypography variant="body2" color="secondary">
            {language === 'ar'
              ? 'مرحباً بك في رحلة ريادة الأعمال! نحن هنا لدعمك في كل خطوة.'
              : 'Welcome to your entrepreneurial journey! We\'re here to support you every step of the way.'
            }
          </ArabicTypography>
        </div>
      </div>
    </div>
  );
};

export default RegistrationSuccessPage;
