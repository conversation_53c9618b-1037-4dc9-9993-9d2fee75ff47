/**
 * Enhanced ML Insights Component
 * Displays advanced machine learning insights and predictions
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { 
  TrendingUp, 
  Target, 
  DollarSign, 
  Users, 
  Calendar,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  <PERSON><PERSON>hart,
  Activity
} from 'lucide-react';

interface MLInsight {
  type: 'success_probability' | 'market_timing' | 'risk_assessment' | 'customer_acquisition' | 'revenue_forecast';
  title: string;
  value: string | number;
  confidence: number;
  trend: 'up' | 'down' | 'stable';
  details: string;
  recommendations?: string[];
}

interface EnhancedMLInsightsProps {
  data?: {
    insights?: MLInsight[];
    predictions?: any;
    recommendations?: string[];
  };
  className?: string;
}

const EnhancedMLInsights: React.FC<EnhancedMLInsightsProps> = ({
  data,
  className = ''
}) => {
  const { t } = useTranslation();

  // Default insights if no data provided
  const defaultInsights: MLInsight[] = [
    {
      type: 'success_probability',
      title: t('ml.insights.successProbability', 'Success Probability'),
      value: '72%',
      confidence: 85,
      trend: 'up',
      details: t('ml.insights.successDetails', 'Based on market conditions and business model analysis'),
      recommendations: [
        t('ml.insights.focusOnCustomerAcquisition', 'Focus on customer acquisition strategies'),
        t('ml.insights.improveProductMarketFit', 'Improve product-market fit')
      ]
    },
    {
      type: 'market_timing',
      title: t('ml.insights.marketTiming', 'Market Timing'),
      value: t('ml.insights.optimal', 'Optimal'),
      confidence: 78,
      trend: 'up',
      details: t('ml.insights.timingDetails', 'Current market conditions favor your business model'),
      recommendations: [
        t('ml.insights.launchSoon', 'Consider launching within the next 6 months'),
        t('ml.insights.capitalizeOnTrends', 'Capitalize on current market trends')
      ]
    },
    {
      type: 'risk_assessment',
      title: t('ml.insights.riskLevel', 'Risk Level'),
      value: t('ml.insights.moderate', 'Moderate'),
      confidence: 82,
      trend: 'stable',
      details: t('ml.insights.riskDetails', 'Manageable risks with proper mitigation strategies'),
      recommendations: [
        t('ml.insights.diversifyRevenue', 'Diversify revenue streams'),
        t('ml.insights.buildReserves', 'Build financial reserves')
      ]
    }
  ];

  const insights = data?.insights || defaultInsights;

  const getInsightIcon = (type: MLInsight['type']) => {
    switch (type) {
      case 'success_probability':
        return <Target className="w-5 h-5 text-green-400" />;
      case 'market_timing':
        return <Calendar className="w-5 h-5 text-blue-400" />;
      case 'risk_assessment':
        return <AlertTriangle className="w-5 h-5 text-yellow-400" />;
      case 'customer_acquisition':
        return <Users className="w-5 h-5 text-purple-400" />;
      case 'revenue_forecast':
        return <DollarSign className="w-5 h-5 text-green-400" />;
      default:
        return <BarChart3 className="w-5 h-5 text-gray-400" />;
    }
  };

  const getTrendIcon = (trend: MLInsight['trend']) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-400" />;
      case 'down':
        return <TrendingUp className="w-4 h-4 text-red-400 rotate-180" />;
      case 'stable':
        return <Activity className="w-4 h-4 text-yellow-400" />;
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-400';
    if (confidence >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg">
          <PieChart className="w-6 h-6 text-white" />
        </div>
        <div>
          <h3 className="text-xl font-semibold text-white">
            {t('ml.insights.title', 'Enhanced ML Insights')}
          </h3>
          <p className="text-gray-300 text-sm">
            {t('ml.insights.subtitle', 'AI-powered business intelligence and predictions')}
          </p>
        </div>
      </div>

      {/* Insights Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {insights.map((insight, index) => (
          <div
            key={index}
            className="glass-light rounded-xl p-6 border border-glass-border hover:border-purple-500/30 transition-colors"
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                {getInsightIcon(insight.type)}
                <h4 className="font-medium text-white">{insight.title}</h4>
              </div>
              {getTrendIcon(insight.trend)}
            </div>

            {/* Value */}
            <div className="mb-3">
              <div className="text-2xl font-bold text-white mb-1">
                {insight.value}
              </div>
              <div className={`text-sm ${getConfidenceColor(insight.confidence)}`}>
                {insight.confidence}% {t('ml.insights.confidence', 'confidence')}
              </div>
            </div>

            {/* Details */}
            <p className="text-gray-300 text-sm mb-4">
              {insight.details}
            </p>

            {/* Recommendations */}
            {insight.recommendations && insight.recommendations.length > 0 && (
              <div className="space-y-2">
                <h5 className="text-sm font-medium text-gray-200">
                  {t('ml.insights.recommendations', 'Recommendations')}
                </h5>
                <ul className="space-y-1">
                  {insight.recommendations.map((rec, recIndex) => (
                    <li key={recIndex} className="flex items-start space-x-2 text-xs text-gray-300">
                      <CheckCircle className="w-3 h-3 text-green-400 mt-0.5 flex-shrink-0" />
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Additional Predictions Section */}
      {data?.predictions && (
        <div className="glass-light rounded-xl p-6 border border-glass-border">
          <h4 className="text-lg font-semibold text-white mb-4">
            {t('ml.insights.predictions', 'ML Predictions')}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(data.predictions).map(([key, value]) => (
              <div key={key} className="flex justify-between items-center py-2 border-b border-gray-700 last:border-b-0">
                <span className="text-gray-300 capitalize">{key.replace('_', ' ')}</span>
                <span className="text-white font-medium">{String(value)}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* General Recommendations */}
      {data?.recommendations && data.recommendations.length > 0 && (
        <div className="glass-light rounded-xl p-6 border border-glass-border">
          <h4 className="text-lg font-semibold text-white mb-4">
            {t('ml.insights.generalRecommendations', 'General Recommendations')}
          </h4>
          <ul className="space-y-3">
            {data.recommendations.map((rec, index) => (
              <li key={index} className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                <span className="text-gray-300">{rec}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default EnhancedMLInsights;
