/**
 * UNIFIED ROLE SYSTEM - SINGLE SOURCE OF TRUTH
 * No more duplicate functions - each role has its own dedicated file
 */

import { UserRole } from '../utils/unifiedRoleManager';
import userRole from './user';
import mentorRole from './mentor';
import investorRole from './investor';
import adminRole from './admin';

// Import role configurations
export { default as UserRole } from './user';
export { default as MentorRole } from './mentor';
export { default as InvestorRole } from './investor';
export { default as AdminRole } from './admin';

// Role registry - single source of truth
export const ROLE_REGISTRY = {
  user: userRole,
  mentor: mentorR<PERSON>,
  investor: investorRole,
  admin: adminRole,
  moderator: userRole, // Moderator uses user config for now
  super_admin: adminRole // Super admin extends admin config
} as const;



/**
 * Get role configuration for a specific role
 */
export function getRoleConfig(role: UserRole) {
  return ROLE_REGISTRY[role] || ROLE_REGISTRY.user;
}



/**
 * Get routes for a specific role
 */
export function getRoutesForRole(role: UserRole) {
  const config = getRoleConfig(role);
  return config.routes;
}

/**
 * Get dashboard route for a specific role
 */
export function getDashboardRouteForRole(role: UserRole) {
  const config = getRoleConfig(role);
  return config.getDashboardRoute();
}

/**
 * Get permissions for a specific role
 */
export function getPermissionsForRole(role: UserRole) {
  const config = getRoleConfig(role);
  return config.permissions;
}





// ✅ REMOVED: Duplicate role detection - use getUserRole() from unifiedRoleManager instead

/**
 * Browser console helper
 */
if (typeof window !== 'undefined') {
  (window as any).roleSystem = {
    getConfig: getRoleConfig,
    getRoutes: getRoutesForRole,
    getDashboardRoute: getDashboardRouteForRole,
    getPermissions: getPermissionsForRole
    // ✅ REMOVED: debugDetection - use getUserRole() from unifiedRoleManager instead
  };

  console.log('🎯 Role System loaded! Use window.roleSystem for debugging');
}

export default {
  getRoleConfig,
  getRoutesForRole,
  getDashboardRouteForRole,
  getPermissionsForRole
  // ✅ REMOVED: debugRoleDetection - use getUserRole() from unifiedRoleManager instead
};
