/**
 * AI Hooks for Business Analysis and AI Services
 * Provides hooks for interacting with AI services and business analysis
 */

import { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { api } from '../services/api';

// Types for AI analysis
export interface BusinessAnalysisResult {
  id: string;
  businessIdeaId: number;
  analysisType: 'market_research' | 'financial_projection' | 'risk_assessment' | 'competitive_analysis';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  result?: any;
  confidence?: number;
  recommendations?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface AIAnalysisOptions {
  includeMarketResearch?: boolean;
  includeFinancialProjections?: boolean;
  includeRiskAssessment?: boolean;
  includeCompetitiveAnalysis?: boolean;
  language?: string;
}

export interface UseBusinessAnalysisReturn {
  analysis: BusinessAnalysisResult | null;
  isLoading: boolean;
  error: string | null;
  startAnalysis: (businessIdeaId: number, options?: AIAnalysisOptions) => Promise<void>;
  refreshAnalysis: () => Promise<void>;
  clearError: () => void;
}

/**
 * Hook for business analysis using AI
 */
export function useBusinessAnalysis(businessIdeaId?: number): UseBusinessAnalysisReturn {
  const [analysis, setAnalysis] = useState<BusinessAnalysisResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useSelector((state: RootState) => state.auth);

  const startAnalysis = useCallback(async (
    ideaId: number, 
    options: AIAnalysisOptions = {}
  ) => {
    if (!user) {
      setError('User must be authenticated to perform analysis');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Start AI analysis
      const response = await api.post('/ai/business-analysis/', {
        business_idea_id: ideaId,
        analysis_options: {
          include_market_research: options.includeMarketResearch ?? true,
          include_financial_projections: options.includeFinancialProjections ?? true,
          include_risk_assessment: options.includeRiskAssessment ?? true,
          include_competitive_analysis: options.includeCompetitiveAnalysis ?? true,
          language: options.language || 'en'
        }
      });

      setAnalysis(response);
    } catch (err: any) {
      setError(err.message || 'Failed to start business analysis');
      console.error('Business analysis error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  const refreshAnalysis = useCallback(async () => {
    if (!businessIdeaId || !user) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await api.get(`/ai/business-analysis/${businessIdeaId}/`);
      setAnalysis(response);
    } catch (err: any) {
      setError(err.message || 'Failed to refresh analysis');
      console.error('Analysis refresh error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [businessIdeaId, user]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Auto-load analysis if businessIdeaId is provided
  useEffect(() => {
    if (businessIdeaId && user) {
      refreshAnalysis();
    }
  }, [businessIdeaId, user, refreshAnalysis]);

  return {
    analysis,
    isLoading,
    error,
    startAnalysis,
    refreshAnalysis,
    clearError
  };
}

/**
 * Hook for AI chat functionality
 */
export interface UseChatReturn {
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
  sendMessage: (message: string, context?: any) => Promise<void>;
  clearChat: () => void;
  clearError: () => void;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  context?: any;
}

export function useChat(businessIdeaId?: number): UseChatReturn {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useSelector((state: RootState) => state.auth);

  const sendMessage = useCallback(async (message: string, context?: any) => {
    if (!user) {
      setError('User must be authenticated to send messages');
      return;
    }

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: message,
      timestamp: new Date().toISOString(),
      context
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);
    setError(null);

    try {
      const response = await api.post('/ai/chat/', {
        message,
        business_idea_id: businessIdeaId,
        context,
        conversation_history: messages.slice(-10) // Last 10 messages for context
      });

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: response.message || response.response || 'I apologize, but I could not generate a response.',
        timestamp: new Date().toISOString(),
        context: response.context
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (err: any) {
      setError(err.message || 'Failed to send message');
      console.error('Chat error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user, businessIdeaId, messages]);

  const clearChat = useCallback(() => {
    setMessages([]);
    setError(null);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    clearChat,
    clearError
  };
}

/**
 * Hook for AI-powered market research
 */
export interface UseMarketResearchReturn {
  research: any | null;
  isLoading: boolean;
  error: string | null;
  startResearch: (businessIdeaId: number, options?: any) => Promise<void>;
  clearError: () => void;
}

export function useMarketResearch(): UseMarketResearchReturn {
  const [research, setResearch] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useSelector((state: RootState) => state.auth);

  const startResearch = useCallback(async (businessIdeaId: number, options: any = {}) => {
    if (!user) {
      setError('User must be authenticated to perform market research');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await api.post('/ai/market-research/', {
        business_idea_id: businessIdeaId,
        research_options: options
      });

      setResearch(response);
    } catch (err: any) {
      setError(err.message || 'Failed to start market research');
      console.error('Market research error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    research,
    isLoading,
    error,
    startResearch,
    clearError
  };
}

/**
 * Hook for AI predictions and insights
 */
export interface UsePredictionsReturn {
  predictions: any | null;
  isLoading: boolean;
  error: string | null;
  generatePredictions: (businessIdeaId: number) => Promise<void>;
  clearError: () => void;
}

export function usePredictions(): UsePredictionsReturn {
  const [predictions, setPredictions] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useSelector((state: RootState) => state.auth);

  const generatePredictions = useCallback(async (businessIdeaId: number) => {
    if (!user) {
      setError('User must be authenticated to generate predictions');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await api.post('/ai/predictions/', {
        business_idea_id: businessIdeaId
      });

      setPredictions(response);
    } catch (err: any) {
      setError(err.message || 'Failed to generate predictions');
      console.error('Predictions error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    predictions,
    isLoading,
    error,
    generatePredictions,
    clearError
  };
}
