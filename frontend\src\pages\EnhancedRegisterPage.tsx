/**
 * Enhanced Register Page
 * Multi-step registration with role selection matching database schema
 */

import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { useLanguage } from '../hooks/useLanguage';
import { useTranslation } from 'react-i18next';
import { RTLIcon, RTLText } from '../components/common';
import { registerEnhanced } from '../store/authSlice';
import { RootState, AppDispatch } from '../store';
import { productionMonitor } from '../utils/productionMonitor';
import {
  Eye,
  EyeOff,
  User,
  Mail,
  Lock,
  Building,
  Users,
  DollarSign,
  CheckCircle,
  Clock,
  AlertCircle,
  FileText,
  Shield,
  ArrowRight,
  ArrowLeft,
  MapPin,
  Phone,
  UserPlus
} from 'lucide-react';

interface RoleApplication {
  role: 'user' | 'mentor' | 'investor' | 'moderator';
  selected: boolean;
  requiresApproval: boolean;
  additionalInfo: {
    // Common fields
    company?: string;
    industry?: string;
    experience?: string;
    linkedinProfile?: string;

    // Entrepreneur/User specific fields
    projectStage?: string;
    projectDescription?: string;
    fundingNeeded?: string;
    teamSize?: string;
    supportNeeded?: string[];
    previousExperience?: string;

    // Investor specific fields
    investmentCriteria?: string;
    minimumInvestment?: number;
    maximumInvestment?: number;

    // Mentor specific fields
    yearsOfExperience?: number;

    // Moderator specific fields
    moderationExperience?: string;
    motivation?: string;
    qualifications?: string;
    portfolioUrl?: string;
  };
}

interface RegisterFormData {
  // Basic Information
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  phone?: string;
  location: string;
  
  // Role Applications
  roleApplications: RoleApplication[];
  
  // Legal
  agreeToTerms: boolean;
  agreeToPrivacy: boolean;
  subscribeToNewsletter: boolean;
}

const EnhancedRegisterPage: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading, error } = useSelector((state: RootState) => state.auth);
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<RegisterFormData>({
    firstName: '',
    lastName: '',
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    location: '',
    roleApplications: [
      {
        role: 'user',
        selected: false,
        requiresApproval: false,
        additionalInfo: {}
      },
      {
        role: 'mentor',
        selected: false,
        requiresApproval: true,
        additionalInfo: {}
      },
      {
        role: 'investor',
        selected: false,
        requiresApproval: true,
        additionalInfo: {}
      },
      {
        role: 'moderator',
        selected: false,
        requiresApproval: true,
        additionalInfo: {}
      }
    ],
    agreeToTerms: false,
    agreeToPrivacy: false,
    subscribeToNewsletter: true
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const totalSteps = 4;
  const progress = (currentStep / totalSteps) * 100;

  const roleDefinitions = {
    user: {
      title: language === 'ar' ? 'رائد أعمال' : 'Entrepreneur',
      description: language === 'ar'
        ? 'ابدأ رحلتك الريادية مع الدعم الكامل لتطوير فكرتك وتحويلها إلى مشروع ناجح'
        : 'Start your entrepreneurial journey with full support to develop your idea into a successful business',
      icon: <User className="w-6 h-6" />,
      benefits: language === 'ar'
        ? ['إنشاء خطة عمل متكاملة', 'الحصول على التمويل', 'التوجيه من الخبراء', 'الانضمام لبرامج الحاضنة', 'الوصول للشبكة المهنية']
        : ['Create comprehensive business plan', 'Access funding opportunities', 'Expert mentorship', 'Join incubator programs', 'Professional networking'],
      immediate: true
    },
    mentor: {
      title: language === 'ar' ? 'موجه' : 'Mentor',
      description: language === 'ar'
        ? 'شارك خبرتك وساعد الجيل القادم من رواد الأعمال'
        : 'Share your expertise and help the next generation of entrepreneurs',
      icon: <Users className="w-6 h-6" />,
      benefits: language === 'ar'
        ? ['إرشاد رواد الأعمال', 'بناء الشبكة', 'مشاركة الخبرة', 'عوائد مالية']
        : ['Mentor entrepreneurs', 'Build network', 'Share expertise', 'Financial returns'],
      immediate: false,
      approvalNote: language === 'ar' 
        ? 'يتطلب مراجعة الخبرة والمؤهلات'
        : 'Requires review of experience and qualifications'
    },
    investor: {
      title: language === 'ar' ? 'مستثمر' : 'Investor',
      description: language === 'ar'
        ? 'اكتشف واستثمر في الشركات الناشئة الواعدة'
        : 'Discover and invest in promising startups',
      icon: <DollarSign className="w-6 h-6" />,
      benefits: language === 'ar'
        ? ['الوصول للصفقات', 'العناية الواجبة', 'إدارة المحفظة', 'تحليل السوق']
        : ['Deal access', 'Due diligence', 'Portfolio management', 'Market analysis'],
      immediate: false,
      approvalNote: language === 'ar'
        ? 'يتطلب التحقق من الاعتماد والخبرة الاستثمارية'
        : 'Requires verification of accreditation and investment experience'
    },
    moderator: {
      title: language === 'ar' ? 'مشرف' : 'Moderator',
      description: language === 'ar'
        ? 'إدارة المحتوى والمجتمع وضمان جودة المناقشات'
        : 'Manage content and community, ensure quality discussions',
      icon: <Shield className="w-6 h-6" />,
      benefits: language === 'ar'
        ? ['إدارة المحتوى', 'إشراف المجتمع', 'حل النزاعات', 'ضمان الجودة']
        : ['Content management', 'Community oversight', 'Dispute resolution', 'Quality assurance'],
      immediate: false,
      approvalNote: language === 'ar'
        ? 'يتطلب خبرة في إدارة المجتمعات والمحتوى'
        : 'Requires experience in community and content management'
    }
  };

  const handleInputChange = (field: keyof RegisterFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleRoleToggle = (roleIndex: number) => {
    setFormData(prev => ({
      ...prev,
      roleApplications: prev.roleApplications.map((role, index) =>
        index === roleIndex
          ? { ...role, selected: true }
          : { ...role, selected: false } // Deselect all other roles
      )
    }));
  };

  const handleRoleInfoChange = (roleIndex: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      roleApplications: prev.roleApplications.map((role, index) => 
        index === roleIndex 
          ? { 
              ...role, 
              additionalInfo: { 
                ...role.additionalInfo, 
                [field]: value 
              }
            }
          : role
      )
    }));
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!formData.firstName.trim()) {
          newErrors.firstName = language === 'ar' ? 'الاسم الأول مطلوب' : 'First name is required';
        }
        if (!formData.lastName.trim()) {
          newErrors.lastName = language === 'ar' ? 'اسم العائلة مطلوب' : 'Last name is required';
        }
        if (!formData.username.trim()) {
          newErrors.username = language === 'ar' ? 'اسم المستخدم مطلوب' : 'Username is required';
        } else if (formData.username.length < 3) {
          newErrors.username = language === 'ar' ? 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل' : 'Username must be at least 3 characters';
        } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
          newErrors.username = language === 'ar' ? 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط' : 'Username can only contain letters, numbers, and underscores';
        }
        if (!formData.email.trim()) {
          newErrors.email = language === 'ar' ? 'البريد الإلكتروني مطلوب' : 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
          newErrors.email = language === 'ar' ? 'البريد الإلكتروني غير صحيح' : 'Invalid email format';
        }
        if (!formData.password) {
          newErrors.password = language === 'ar' ? 'كلمة المرور مطلوبة' : 'Password is required';
        } else if (formData.password.length < 8) {
          newErrors.password = language === 'ar' ? 'كلمة المرور يجب أن تكون 8 أحرف على الأقل' : 'Password must be at least 8 characters';
        }
        if (formData.password !== formData.confirmPassword) {
          newErrors.confirmPassword = language === 'ar' ? 'كلمات المرور غير متطابقة' : 'Passwords do not match';
        }
        if (!formData.location.trim()) {
          newErrors.location = language === 'ar' ? 'الموقع مطلوب' : 'Location is required';
        }
        break;

      case 2:
        const selectedRoles = formData.roleApplications.filter(role => role.selected);
        if (selectedRoles.length === 0) {
          newErrors.roles = language === 'ar' ? 'يجب اختيار دور واحد' : 'Please select a role';
        }
        break;

      case 4:
        if (!formData.agreeToTerms) {
          newErrors.agreeToTerms = language === 'ar' ? 'يجب الموافقة على الشروط والأحكام' : 'You must agree to the terms and conditions';
        }
        if (!formData.agreeToPrivacy) {
          newErrors.agreeToPrivacy = language === 'ar' ? 'يجب الموافقة على سياسة الخصوصية' : 'You must agree to the privacy policy';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;

    setLoading(true);
    try {
      // Get the selected role
      const selectedRoleApp = formData.roleApplications.find(role => role.selected);
      if (!selectedRoleApp) {
        throw new Error('No role selected');
      }

      // Prepare the registration data for backend (convert field names)
      const registrationData = {
        username: formData.username,
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
        password: formData.password,
        confirm_password: formData.confirmPassword,
        phone: formData.phone || '',
        location: formData.location,
        selected_role: selectedRoleApp.role,
        role_additional_info: selectedRoleApp.additionalInfo
      };

      console.log('Sending registration data:', registrationData);

      // Track registration start time for performance monitoring
      const registrationStartTime = Date.now();
      try {
        productionMonitor.recordCustomEvent('registration_started', {
          role: selectedRoleApp.role,
          hasPhone: !!formData.phone,
          location: formData.location
        });
      } catch (monitoringError) {
        console.warn('Registration monitoring failed:', monitoringError);
      }

      // Use Redux to handle registration
      const result = await dispatch(registerEnhanced(registrationData)).unwrap();

      // Track successful registration
      const registrationTime = Date.now() - registrationStartTime;
      productionMonitor.recordMetric('registration_success_time', registrationTime);
      productionMonitor.recordCustomEvent('registration_completed', {
        role: selectedRoleApp.role,
        userId: result.id,
        registrationTime
      });

      console.log('Registration successful:', result);

      // Navigate to success page
      navigate('/register/success', {
        state: {
          userData: registrationData,
          user: result
        }
      });
    } catch (error: any) {
      console.error('Registration error:', error);

      // Track registration failure
      productionMonitor.recordError({
        message: `Registration failed: ${error.message || error}`,
        url: window.location.href,
        severity: 'medium'
      });

      try {
        const failedRole = formData.roleApplications.find(role => role.selected)?.role || 'unknown';
        productionMonitor.recordCustomEvent('registration_failed', {
          role: failedRole,
          errorMessage: error.message || error
        });
      } catch (monitoringError) {
        console.warn('Registration error monitoring failed:', monitoringError);
      }

      setErrors({ submit: language === 'ar' ? 'حدث خطأ أثناء التسجيل' : `Registration failed: ${error}` });
    } finally {
      setLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-600/20 mb-4">
                <RTLIcon icon={UserPlus} size={32} className="text-purple-400" flipInRTL={true} />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">
                {language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information'}
              </h3>
              <RTLText as="p" align="center" className="text-gray-300">
                {language === 'ar' ? 'أدخل معلوماتك الشخصية' : 'Enter your personal information'}
              </RTLText>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'الاسم الأول' : 'First Name'} <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={language === 'ar' ? 'أدخل الاسم الأول' : 'Enter first name'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                  autoComplete="given-name"
                  aria-required="true"
                  aria-describedby={errors.firstName ? 'firstName-error' : undefined}
                  aria-invalid={errors.firstName ? 'true' : 'false'}
                />
                {errors.firstName && (
                  <p id="firstName-error" className="text-red-400 text-sm mt-1" role="alert">
                    {errors.firstName}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'اسم العائلة' : 'Last Name'} <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={language === 'ar' ? 'أدخل اسم العائلة' : 'Enter last name'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                  autoComplete="family-name"
                  aria-required="true"
                  aria-describedby={errors.lastName ? 'lastName-error' : undefined}
                  aria-invalid={errors.lastName ? 'true' : 'false'}
                />
                {errors.lastName && (
                  <p id="lastName-error" className="text-red-400 text-sm mt-1" role="alert">
                    {errors.lastName}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {language === 'ar' ? 'اسم المستخدم' : 'Username'} <span className="text-red-400">*</span>
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User className="w-5 h-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={language === 'ar' ? 'أدخل اسم المستخدم' : 'Enter username'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                  autoComplete="username"
                  aria-required="true"
                  aria-describedby={errors.username ? 'username-error' : undefined}
                  aria-invalid={errors.username ? 'true' : 'false'}
                />
              </div>
              {errors.username && (
                <p id="username-error" className="text-red-400 text-sm mt-1" role="alert">
                  {errors.username}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {language === 'ar' ? 'البريد الإلكتروني' : 'Email Address'} <span className="text-red-400">*</span>
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="w-5 h-5 text-gray-400" />
                </div>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={language === 'ar' ? 'أدخل البريد الإلكتروني' : 'Enter email address'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                  autoComplete="email"
                  aria-required="true"
                  aria-describedby={errors.email ? 'email-error' : undefined}
                  aria-invalid={errors.email ? 'true' : 'false'}
                />
              </div>
              {errors.email && (
                <p id="email-error" className="text-red-400 text-sm mt-1" role="alert">
                  {errors.email}
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'كلمة المرور' : 'Password'} <span className="text-red-400">*</span>
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="w-5 h-5 text-gray-400" />
                  </div>
                  <input
                    type={showPassword ? 'text' : 'password'}
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    className="w-full pl-10 pr-12 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                    placeholder={language === 'ar' ? 'أدخل كلمة المرور' : 'Enter password'}
                    dir={language === 'ar' ? 'rtl' : 'ltr'}
                    autoComplete="new-password"
                    aria-required="true"
                    aria-describedby={errors.password ? 'password-error' : 'password-help'}
                    aria-invalid={errors.password ? 'true' : 'false'}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-300"
                    aria-label={showPassword ?
                      (language === 'ar' ? 'إخفاء كلمة المرور' : 'Hide password') :
                      (language === 'ar' ? 'إظهار كلمة المرور' : 'Show password')
                    }
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
                <p id="password-help" className="text-gray-400 text-xs mt-1">
                  {language === 'ar' ? 'يجب أن تكون كلمة المرور 8 أحرف على الأقل' : 'Password must be at least 8 characters'}
                </p>
                {errors.password && (
                  <p id="password-error" className="text-red-400 text-sm mt-1" role="alert">
                    {errors.password}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'تأكيد كلمة المرور' : 'Confirm Password'} <span className="text-red-400">*</span>
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="w-5 h-5 text-gray-400" />
                  </div>
                  <input
                    type={showConfirmPassword ? 'text' : 'password'}
                    id="confirmPassword"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                    className="w-full pl-10 pr-12 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                    placeholder={language === 'ar' ? 'أكد كلمة المرور' : 'Confirm password'}
                    dir={language === 'ar' ? 'rtl' : 'ltr'}
                    autoComplete="new-password"
                    aria-required="true"
                    aria-describedby={errors.confirmPassword ? 'confirmPassword-error' : undefined}
                    aria-invalid={errors.confirmPassword ? 'true' : 'false'}
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-300"
                    aria-label={showConfirmPassword ?
                      (language === 'ar' ? 'إخفاء تأكيد كلمة المرور' : 'Hide confirm password') :
                      (language === 'ar' ? 'إظهار تأكيد كلمة المرور' : 'Show confirm password')
                    }
                  >
                    {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p id="confirmPassword-error" className="text-red-400 text-sm mt-1" role="alert">
                    {errors.confirmPassword}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'رقم الهاتف (اختياري)' : 'Phone Number (Optional)'}
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="w-5 h-5 text-gray-400" />
                  </div>
                  <input
                    type="tel"
                    value={formData.phone || ''}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                    placeholder={language === 'ar' ? 'أدخل رقم الهاتف' : 'Enter phone number'}
                    dir={language === 'ar' ? 'rtl' : 'ltr'}
                    autoComplete="off"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'الموقع' : 'Location'} <span className="text-red-400">*</span>
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MapPin className="w-5 h-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                    placeholder={language === 'ar' ? 'مثال: الرياض، السعودية' : 'e.g., Riyadh, Saudi Arabia'}
                    dir={language === 'ar' ? 'rtl' : 'ltr'}
                    autoComplete="off"
                  />
                </div>
                {errors.location && (
                  <p className="text-red-400 text-sm mt-1">{errors.location}</p>
                )}
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-600/20 mb-4">
                <RTLIcon icon={Users} size={32} className="text-purple-400" flipInRTL={true} />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">
                {language === 'ar' ? 'اختر أدوارك' : 'Choose Your Roles'}
              </h3>
              <RTLText as="p" align="center" className="text-gray-300">
                {language === 'ar' ? 'يمكنك اختيار أكثر من دور واحد' : 'You can select multiple roles'}
              </RTLText>
            </div>

            {errors.roles && (
              <div className="bg-red-500/20 border border-red-400/30 rounded-lg p-4 mb-6">
                <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <AlertCircle className={`w-5 h-5 text-red-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  <p className="text-red-300">
                    {errors.roles}
                  </p>
                </div>
              </div>
            )}

            <div className="space-y-4">
              {formData.roleApplications.map((roleApp, index) => {
                const roleDef = roleDefinitions[roleApp.role];
                return (
                  <div
                    key={roleApp.role}
                    className={`border-2 rounded-lg p-6 cursor-pointer transition-all ${
                      roleApp.selected
                        ? 'border-purple-400 bg-purple-500/20 shadow-lg'
                        : 'border-white/30 bg-white/10 hover:border-purple-400/50 hover:bg-white/20'
                    }`}
                    onClick={() => handleRoleToggle(index)}
                  >
                    <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className={`flex-shrink-0 ${isRTL ? 'ml-4' : 'mr-4'}`}>
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                          roleApp.selected ? 'bg-purple-500/30 text-purple-300' : 'bg-white/20 text-gray-300'
                        }`}>
                          {roleDef.icon}
                        </div>
                      </div>
                      
                      <div className="flex-1">
                        <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <h5 className="text-lg font-semibold text-white">
                            {roleDef.title}
                          </h5>
                          
                          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            {roleDef.immediate ? (
                              <span className="px-3 py-1 bg-green-500/20 text-green-300 border border-green-400/30 rounded-full text-sm font-semibold">
                                {language === 'ar' ? 'فوري' : 'Immediate'}
                              </span>
                            ) : (
                              <span className="px-3 py-1 bg-yellow-500/20 text-yellow-300 border border-yellow-400/30 rounded-full text-sm font-semibold">
                                {language === 'ar' ? 'يتطلب موافقة' : 'Requires Approval'}
                              </span>
                            )}
                            
                            <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${isRTL ? 'mr-3' : 'ml-3'} ${
                              roleApp.selected 
                                ? 'border-blue-500 bg-blue-500' 
                                : 'border-gray-300'
                            }`}>
                              {roleApp.selected && (
                                <CheckCircle className="w-3 h-3 text-white" />
                              )}
                            </div>
                          </div>
                        </div>

                        <p className="text-gray-300 text-sm mt-2">
                          {roleDef.description}
                        </p>
                        
                        {!roleDef.immediate && 'approvalNote' in roleDef && roleDef.approvalNote && (
                          <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                              <Clock className={`w-4 h-4 text-yellow-600 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                              <span className="text-yellow-300 text-sm">
                                {roleDef.approvalNote}
                              </span>
                            </div>
                          </div>
                        )}
                        
                        <div className="mt-4">
                          <p className="text-white font-semibold mb-2">
                            {language === 'ar' ? 'المزايا:' : 'Benefits:'}
                          </p>
                          <div className="grid grid-cols-2 gap-2">
                            {roleDef.benefits.map((benefit, benefitIndex) => (
                              <div key={benefitIndex} className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                                <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                                <span className="text-gray-300 text-sm">
                                  {benefit}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-600/20 mb-4">
                <RTLIcon icon={FileText} size={32} className="text-purple-400" flipInRTL={true} />
              </div>
              <h3 className="text-2xl font-bold text-white">
                {language === 'ar' ? 'معلومات إضافية' : 'Additional Information'}
              </h3>
              <RTLText as="p" align="center" className="text-gray-300">
                {language === 'ar' ? 'أكمل معلومات الأدوار المختارة' : 'Complete information for selected roles'}
              </RTLText>
            </div>

            {formData.roleApplications.filter(role => role.selected).map((roleApp) => {
              const originalIndex = formData.roleApplications.findIndex(r => r.role === roleApp.role);
              return (
                <div key={roleApp.role} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                  <h4 className="text-lg font-semibold text-white mb-4">
                    {roleDefinitions[roleApp.role].title}
                  </h4>
                  {roleApp.role === 'user' && (
                    <div className="space-y-6">
                      {/* Business Information */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            {language === 'ar' ? 'اسم الشركة/المشروع' : 'Company/Project Name'} <span className="text-red-400">*</span>
                          </label>
                          <input
                            type="text"
                            value={roleApp.additionalInfo.company || ''}
                            onChange={(e) => handleRoleInfoChange(originalIndex, 'company', e.target.value)}
                            className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                            placeholder={language === 'ar' ? 'اسم شركتك أو مشروعك' : 'Your company or project name'}
                            dir={language === 'ar' ? 'rtl' : 'ltr'}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            {language === 'ar' ? 'مرحلة المشروع' : 'Project Stage'} <span className="text-red-400">*</span>
                          </label>
                          <select
                            value={roleApp.additionalInfo.projectStage || ''}
                            onChange={(e) => handleRoleInfoChange(originalIndex, 'projectStage', e.target.value)}
                            className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                            dir={language === 'ar' ? 'rtl' : 'ltr'}
                          >
                            <option value="">{language === 'ar' ? 'اختر مرحلة المشروع' : 'Select Project Stage'}</option>
                            <option value="idea">{language === 'ar' ? 'مجرد فكرة' : 'Just an Idea'}</option>
                            <option value="planning">{language === 'ar' ? 'مرحلة التخطيط' : 'Planning Stage'}</option>
                            <option value="prototype">{language === 'ar' ? 'نموذج أولي' : 'Prototype'}</option>
                            <option value="mvp">{language === 'ar' ? 'منتج أولي' : 'MVP (Minimum Viable Product)'}</option>
                            <option value="launched">{language === 'ar' ? 'تم الإطلاق' : 'Launched'}</option>
                            <option value="scaling">{language === 'ar' ? 'مرحلة التوسع' : 'Scaling'}</option>
                          </select>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          {language === 'ar' ? 'الصناعة/القطاع' : 'Industry/Sector'} <span className="text-red-400">*</span>
                        </label>
                        <select
                          value={roleApp.additionalInfo.industry || ''}
                          onChange={(e) => handleRoleInfoChange(originalIndex, 'industry', e.target.value)}
                          className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                          dir={language === 'ar' ? 'rtl' : 'ltr'}
                        >
                          <option value="">{language === 'ar' ? 'اختر الصناعة' : 'Select Industry'}</option>
                          <option value="fintech">{language === 'ar' ? 'التكنولوجيا المالية' : 'FinTech'}</option>
                          <option value="healthtech">{language === 'ar' ? 'التكنولوجيا الصحية' : 'HealthTech'}</option>
                          <option value="edtech">{language === 'ar' ? 'التكنولوجيا التعليمية' : 'EdTech'}</option>
                          <option value="ecommerce">{language === 'ar' ? 'التجارة الإلكترونية' : 'E-commerce'}</option>
                          <option value="logistics">{language === 'ar' ? 'اللوجستيات والنقل' : 'Logistics & Transportation'}</option>
                          <option value="foodtech">{language === 'ar' ? 'تكنولوجيا الطعام' : 'Food Technology'}</option>
                          <option value="proptech">{language === 'ar' ? 'تكنولوجيا العقارات' : 'PropTech'}</option>
                          <option value="cleantech">{language === 'ar' ? 'التكنولوجيا النظيفة' : 'CleanTech'}</option>
                          <option value="retail">{language === 'ar' ? 'التجارة والبيع بالتجزئة' : 'Retail'}</option>
                          <option value="manufacturing">{language === 'ar' ? 'التصنيع' : 'Manufacturing'}</option>
                          <option value="agriculture">{language === 'ar' ? 'الزراعة' : 'Agriculture'}</option>
                          <option value="entertainment">{language === 'ar' ? 'الترفيه والإعلام' : 'Entertainment & Media'}</option>
                          <option value="other">{language === 'ar' ? 'أخرى' : 'Other'}</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          {language === 'ar' ? 'وصف المشروع/الفكرة' : 'Project/Idea Description'} <span className="text-red-400">*</span>
                        </label>
                        <textarea
                          value={roleApp.additionalInfo.projectDescription || ''}
                          onChange={(e) => handleRoleInfoChange(originalIndex, 'projectDescription', e.target.value)}
                          className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                          rows={4}
                          placeholder={language === 'ar' ? 'اشرح فكرة مشروعك والمشكلة التي يحلها...' : 'Describe your project idea and the problem it solves...'}
                          dir={language === 'ar' ? 'rtl' : 'ltr'}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            {language === 'ar' ? 'التمويل المطلوب (بالدولار)' : 'Funding Needed (USD)'}
                          </label>
                          <select
                            value={roleApp.additionalInfo.fundingNeeded || ''}
                            onChange={(e) => handleRoleInfoChange(originalIndex, 'fundingNeeded', e.target.value)}
                            className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                            dir={language === 'ar' ? 'rtl' : 'ltr'}
                          >
                            <option value="">{language === 'ar' ? 'اختر المبلغ المطلوب' : 'Select Funding Amount'}</option>
                            <option value="0-10k">{language === 'ar' ? 'أقل من 10,000 دولار' : 'Less than $10,000'}</option>
                            <option value="10k-50k">{language === 'ar' ? '10,000 - 50,000 دولار' : '$10,000 - $50,000'}</option>
                            <option value="50k-100k">{language === 'ar' ? '50,000 - 100,000 دولار' : '$50,000 - $100,000'}</option>
                            <option value="100k-500k">{language === 'ar' ? '100,000 - 500,000 دولار' : '$100,000 - $500,000'}</option>
                            <option value="500k-1m">{language === 'ar' ? '500,000 - 1,000,000 دولار' : '$500,000 - $1,000,000'}</option>
                            <option value="1m+">{language === 'ar' ? 'أكثر من مليون دولار' : 'More than $1,000,000'}</option>
                            <option value="no-funding">{language === 'ar' ? 'لا أحتاج تمويل حالياً' : 'No funding needed currently'}</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            {language === 'ar' ? 'حجم الفريق' : 'Team Size'}
                          </label>
                          <select
                            value={roleApp.additionalInfo.teamSize || ''}
                            onChange={(e) => handleRoleInfoChange(originalIndex, 'teamSize', e.target.value)}
                            className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                            dir={language === 'ar' ? 'rtl' : 'ltr'}
                          >
                            <option value="">{language === 'ar' ? 'اختر حجم الفريق' : 'Select Team Size'}</option>
                            <option value="solo">{language === 'ar' ? 'مؤسس واحد' : 'Solo Founder'}</option>
                            <option value="2-3">{language === 'ar' ? '2-3 أشخاص' : '2-3 People'}</option>
                            <option value="4-10">{language === 'ar' ? '4-10 أشخاص' : '4-10 People'}</option>
                            <option value="11-25">{language === 'ar' ? '11-25 شخص' : '11-25 People'}</option>
                            <option value="25+">{language === 'ar' ? 'أكثر من 25 شخص' : 'More than 25 People'}</option>
                          </select>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          {language === 'ar' ? 'ما نوع الدعم الذي تحتاجه؟' : 'What type of support do you need?'}
                        </label>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                          {[
                            { key: 'mentorship', ar: 'التوجيه والإرشاد', en: 'Mentorship' },
                            { key: 'funding', ar: 'التمويل', en: 'Funding' },
                            { key: 'technical', ar: 'الدعم التقني', en: 'Technical Support' },
                            { key: 'marketing', ar: 'التسويق', en: 'Marketing' },
                            { key: 'legal', ar: 'الاستشارات القانونية', en: 'Legal Advice' },
                            { key: 'networking', ar: 'التواصل والشبكات', en: 'Networking' }
                          ].map((support) => (
                            <label key={support.key} className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                              <input
                                type="checkbox"
                                checked={(roleApp.additionalInfo.supportNeeded || []).includes(support.key)}
                                onChange={(e) => {
                                  const currentSupport = roleApp.additionalInfo.supportNeeded || [];
                                  const newSupport = e.target.checked
                                    ? [...currentSupport, support.key]
                                    : currentSupport.filter(s => s !== support.key);
                                  handleRoleInfoChange(originalIndex, 'supportNeeded', newSupport);
                                }}
                                className={`${isRTL ? 'ml-2' : 'mr-2'} text-purple-600 focus:ring-purple-500`}
                              />
                              <span className="text-gray-300 text-sm">
                                {language === 'ar' ? support.ar : support.en}
                              </span>
                            </label>
                          ))}
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          {language === 'ar' ? 'خبرتك السابقة في ريادة الأعمال' : 'Previous Entrepreneurship Experience'}
                        </label>
                        <textarea
                          value={roleApp.additionalInfo.previousExperience || ''}
                          onChange={(e) => handleRoleInfoChange(originalIndex, 'previousExperience', e.target.value)}
                          className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                          rows={3}
                          placeholder={language === 'ar' ? 'اذكر خبرتك السابقة في ريادة الأعمال أو المشاريع...' : 'Describe your previous entrepreneurship or project experience...'}
                          dir={language === 'ar' ? 'rtl' : 'ltr'}
                        />
                      </div>
                    </div>
                  )}

                  {roleApp.role === 'mentor' && (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                          {language === 'ar' ? 'سنوات الخبرة' : 'Years of Experience'}
                        </label>
                        <select
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                          value={roleApp.additionalInfo.yearsOfExperience || ''}
                          onChange={(e) => handleRoleInfoChange(originalIndex, 'yearsOfExperience', parseInt(e.target.value))}
                          dir={isRTL ? 'rtl' : 'ltr'}
                        >
                          <option value="">{language === 'ar' ? 'اختر سنوات الخبرة' : 'Select Years of Experience'}</option>
                          <option value="5">5-10 {language === 'ar' ? 'سنوات' : 'years'}</option>
                          <option value="10">10-15 {language === 'ar' ? 'سنوات' : 'years'}</option>
                          <option value="15">15+ {language === 'ar' ? 'سنوات' : 'years'}</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                          {language === 'ar' ? 'مجالات الخبرة' : 'Areas of Expertise'}
                        </label>
                        <textarea
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                          rows={3}
                          value={roleApp.additionalInfo.experience || ''}
                          onChange={(e) => handleRoleInfoChange(originalIndex, 'experience', e.target.value)}
                          placeholder={language === 'ar' ? 'اذكر مجالات خبرتك...' : 'List your areas of expertise...'}
                          dir={isRTL ? 'rtl' : 'ltr'}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          {language === 'ar' ? 'رابط LinkedIn' : 'LinkedIn Profile'}
                        </label>
                        <input
                          type="url"
                          value={roleApp.additionalInfo.linkedinProfile || ''}
                          onChange={(e) => handleRoleInfoChange(originalIndex, 'linkedinProfile', e.target.value)}
                          className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                          placeholder="https://linkedin.com/in/yourprofile"
                          dir={language === 'ar' ? 'rtl' : 'ltr'}
                        />
                      </div>
                    </div>
                  )}

                  {roleApp.role === 'investor' && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            {language === 'ar' ? 'الحد الأدنى للاستثمار' : 'Minimum Investment'}
                          </label>
                          <input
                            type="number"
                            value={roleApp.additionalInfo.minimumInvestment?.toString() || ''}
                            onChange={(e) => handleRoleInfoChange(originalIndex, 'minimumInvestment', parseInt(e.target.value) || 0)}
                            className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                            placeholder="50000"
                            dir={language === 'ar' ? 'rtl' : 'ltr'}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            {language === 'ar' ? 'الحد الأقصى للاستثمار' : 'Maximum Investment'}
                          </label>
                          <input
                            type="number"
                            value={roleApp.additionalInfo.maximumInvestment?.toString() || ''}
                            onChange={(e) => handleRoleInfoChange(originalIndex, 'maximumInvestment', parseInt(e.target.value) || 0)}
                            className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                            placeholder="1000000"
                            dir={language === 'ar' ? 'rtl' : 'ltr'}
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                          {language === 'ar' ? 'معايير الاستثمار' : 'Investment Criteria'}
                        </label>
                        <textarea
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                          rows={3}
                          value={roleApp.additionalInfo.investmentCriteria || ''}
                          onChange={(e) => handleRoleInfoChange(originalIndex, 'investmentCriteria', e.target.value)}
                          placeholder={language === 'ar' ? 'اذكر معايير الاستثمار المفضلة...' : 'Describe your investment criteria...'}
                          dir={isRTL ? 'rtl' : 'ltr'}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          {language === 'ar' ? 'رابط LinkedIn' : 'LinkedIn Profile'}
                        </label>
                        <input
                          type="url"
                          value={roleApp.additionalInfo.linkedinProfile || ''}
                          onChange={(e) => handleRoleInfoChange(originalIndex, 'linkedinProfile', e.target.value)}
                          className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                          placeholder="https://linkedin.com/in/yourprofile"
                          dir={language === 'ar' ? 'rtl' : 'ltr'}
                        />
                      </div>
                    </div>
                  )}

                  {roleApp.role === 'moderator' && (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                          {language === 'ar' ? 'خبرة الإشراف' : 'Moderation Experience'}
                        </label>
                        <textarea
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                          rows={3}
                          value={roleApp.additionalInfo.moderationExperience || ''}
                          onChange={(e) => handleRoleInfoChange(originalIndex, 'moderationExperience', e.target.value)}
                          placeholder={language === 'ar' ? 'اذكر خبرتك في إدارة المجتمعات...' : 'Describe your community management experience...'}
                          dir={isRTL ? 'rtl' : 'ltr'}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                          {language === 'ar' ? 'الدافع للإشراف' : 'Motivation for Moderation'}
                        </label>
                        <textarea
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                          rows={3}
                          value={roleApp.additionalInfo.motivation || ''}
                          onChange={(e) => handleRoleInfoChange(originalIndex, 'motivation', e.target.value)}
                          placeholder={language === 'ar' ? 'لماذا تريد أن تكون مشرفاً؟' : 'Why do you want to be a moderator?'}
                          dir={isRTL ? 'rtl' : 'ltr'}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          {language === 'ar' ? 'المؤهلات' : 'Qualifications'}
                        </label>
                        <textarea
                          value={roleApp.additionalInfo.qualifications || ''}
                          onChange={(e) => handleRoleInfoChange(originalIndex, 'qualifications', e.target.value)}
                          className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400 min-h-[100px]"
                          placeholder={language === 'ar' ? 'اذكر مؤهلاتك ذات الصلة...' : 'List relevant qualifications...'}
                          dir={language === 'ar' ? 'rtl' : 'ltr'}
                        />
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-600/20 mb-4">
                <RTLIcon icon={CheckCircle} size={32} className="text-purple-400" flipInRTL={false} />
              </div>
              <h3 className="text-2xl font-bold text-white">
                {language === 'ar' ? 'الشروط والأحكام' : 'Terms & Conditions'}
              </h3>
              <RTLText as="p" align="center" className="text-gray-300">
                {language === 'ar' ? 'راجع واقبل الشروط لإكمال التسجيل' : 'Review and accept terms to complete registration'}
              </RTLText>
            </div>

            <div className="space-y-6">
              <div className="border border-gray-200 rounded-lg p-4">
                <label className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <input
                    type="checkbox"
                    checked={formData.agreeToTerms}
                    onChange={(e) => handleInputChange('agreeToTerms', e.target.checked)}
                    className={`mt-1 ${isRTL ? 'ml-3' : 'mr-3'}`}
                  />
                  <div>
                    <span className="text-white">
                      {language === 'ar'
                        ? 'أوافق على '
                        : 'I agree to the '
                      }
                      <Link to="/terms" className="text-purple-400 hover:text-purple-300 font-semibold">
                        {language === 'ar' ? 'الشروط والأحكام' : 'Terms and Conditions'}
                      </Link>
                    </span>
                    {errors.agreeToTerms && (
                      <span className="text-red-400 text-sm mt-1 block">
                        {errors.agreeToTerms}
                      </span>
                    )}
                  </div>
                </label>
              </div>

              <div className="border border-gray-200 rounded-lg p-4">
                <label className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <input
                    type="checkbox"
                    checked={formData.agreeToPrivacy}
                    onChange={(e) => handleInputChange('agreeToPrivacy', e.target.checked)}
                    className={`mt-1 ${isRTL ? 'ml-3' : 'mr-3'}`}
                  />
                  <div>
                    <span className="text-white">
                      {language === 'ar'
                        ? 'أوافق على '
                        : 'I agree to the '
                      }
                      <Link to="/privacy" className="text-purple-400 hover:text-purple-300 font-semibold">
                        {language === 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy'}
                      </Link>
                    </span>
                    {errors.agreeToPrivacy && (
                      <span className="text-red-400 text-sm mt-1 block">
                        {errors.agreeToPrivacy}
                      </span>
                    )}
                  </div>
                </label>
              </div>

              <div className="border border-gray-200 rounded-lg p-4">
                <label className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <input
                    type="checkbox"
                    checked={formData.subscribeToNewsletter}
                    onChange={(e) => handleInputChange('subscribeToNewsletter', e.target.checked)}
                    className={`mt-1 ${isRTL ? 'ml-3' : 'mr-3'}`}
                  />
                  <span className="text-white">
                    {language === 'ar'
                      ? 'أريد تلقي النشرة الإخبارية والتحديثات (اختياري)'
                      : 'I want to receive newsletter and updates (optional)'
                    }
                  </span>
                </label>
              </div>

              {/* Registration Summary */}
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <h4 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'ملخص التسجيل' : 'Registration Summary'}
                </h4>
                <div className="space-y-4">
                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className="text-gray-600 font-arabic">
                      {language === 'ar' ? 'الاسم:' : 'Name:'}
                    </span>
                    <span className="font-semibold font-arabic">
                      {formData.firstName} {formData.lastName}
                    </span>
                  </div>

                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className="text-gray-600 font-arabic">
                      {language === 'ar' ? 'البريد الإلكتروني:' : 'Email:'}
                    </span>
                    <span className="font-semibold font-arabic">
                      {formData.email}
                    </span>
                  </div>

                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className="text-gray-600 font-arabic">
                      {language === 'ar' ? 'الموقع:' : 'Location:'}
                    </span>
                    <span className="font-semibold font-arabic">
                      {formData.location}
                    </span>
                  </div>

                  <div>
                    <span className="text-gray-600 font-arabic">
                      {language === 'ar' ? 'الأدوار المختارة:' : 'Selected Roles:'}
                    </span>
                    <div className="mt-2 space-y-2">
                      {formData.roleApplications.filter(role => role.selected).map((role) => (
                        <div key={role.role} className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <span className="font-semibold font-arabic">
                            {roleDefinitions[role.role].title}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                            role.requiresApproval
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-green-100 text-green-800'
                          } font-arabic`}>
                            {role.requiresApproval
                              ? (language === 'ar' ? 'يتطلب موافقة' : 'Requires Approval')
                              : (language === 'ar' ? 'فوري' : 'Immediate')
                            }
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {errors.submit && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <AlertCircle className={`w-5 h-5 text-red-600 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    <span className="text-red-300">
                      {errors.submit}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center p-4 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-4xl w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <Link to="/" className="inline-block mb-4">
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">
              Yasmeen AI
            </h1>
          </Link>
          <h2 className="text-2xl font-bold text-white mb-2">
            {language === 'ar' ? 'إنشاء حساب جديد' : 'Create Your Account'}
          </h2>
          <p className="text-gray-300">
            {language === 'ar'
              ? 'انضم إلى منصة ريادة الأعمال الرائدة في المنطقة'
              : 'Join the leading entrepreneurship platform in the region'
            }
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="bg-black/30 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-300">
                {language === 'ar' ? 'الخطوة' : 'Step'} {currentStep} {language === 'ar' ? 'من' : 'of'} {totalSteps}
              </span>
              <span className="text-sm text-purple-400">{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-white/20 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={(e) => e.preventDefault()} className="bg-black/30 backdrop-blur-sm rounded-lg p-8 shadow-lg border border-white/20 mb-8">
          {renderStep()}
        </form>

        {/* Navigation */}
        <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className={`px-6 py-3 bg-white/20 border border-white/30 rounded-lg font-medium text-white hover:bg-white/30 transition-all duration-300 flex items-center ${
              currentStep === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-glow'
            } ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            {isRTL ? <ArrowRight className="w-4 h-4 ml-2" /> : <ArrowLeft className="w-4 h-4 mr-2" />}
            {language === 'ar' ? 'السابق' : 'Previous'}
          </button>

          {currentStep === totalSteps ? (
            <button
              onClick={handleSubmit}
              disabled={loading}
              className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium text-white hover:shadow-glow transition-all duration-300 flex items-center"
            >
              {loading ? (
                <span className="inline-block w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
              ) : (
                <CheckCircle className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              )}
              {loading
                ? (language === 'ar' ? 'جاري التسجيل...' : 'Creating Account...')
                : (language === 'ar' ? 'إنشاء الحساب' : 'Create Account')
              }
            </button>
          ) : (
            <button
              onClick={handleNext}
              className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium text-white hover:shadow-glow transition-all duration-300 flex items-center"
            >
              {language === 'ar' ? 'التالي' : 'Next'}
              {isRTL ? <ArrowLeft className="w-4 h-4 ml-2" /> : <ArrowRight className="w-4 h-4 mr-2" />}
            </button>
          )}
        </div>

        {/* Login Link */}
        <div className="text-center mt-8">
          <p className="text-gray-300">
            {language === 'ar' ? 'لديك حساب بالفعل؟' : 'Already have an account?'}{' '}
            <Link to="/login" className="text-purple-400 hover:text-purple-300 transition-colors font-semibold">
              {language === 'ar' ? 'تسجيل الدخول' : 'Sign In'}
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default EnhancedRegisterPage;
