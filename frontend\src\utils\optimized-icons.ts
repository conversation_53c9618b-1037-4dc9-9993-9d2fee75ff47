/**
 * OPTIMIZED ICONS
 * Tree-shaken icon imports to reduce bundle size
 * Only import the icons we actually use
 */

// ✅ PERFORMANCE: Import only specific icons instead of entire lucide-react package
export {
  // Navigation & UI
  Menu,
  X,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  
  // Dashboard & Analytics
  BarChart3,
  LineChart,
  PieChart,
  TrendingUp,
  TrendingDown,
  Activity,
  
  // User & Profile
  User,
  Users,
  UserPlus,
  UserCheck,
  UserX,
  Crown,
  Shield,
  
  // Business & Ideas
  Lightbulb,
  Target,
  Briefcase,
  Building,
  DollarSign,
  TrendingUp as Growth,
  
  // Communication
  MessageSquare,
  MessageCircle,
  Mail,
  Phone,
  Video,
  Mic,
  MicOff,
  
  // Actions
  Plus,
  Edit,
  Trash2,
  Save,
  Download,
  Upload,
  Share,
  Copy,
  
  // Status & Feedback
  Check,
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  Info,
  XCircle,
  Clock,
  ShieldAlert,
  
  // Files & Documents
  File,
  FileText,
  Folder,
  FolderOpen,
  Image,
  Download as FileDownload,
  
  // Settings & Configuration
  Settings,
  Cog,
  Sliders,
  Filter,
  Search,
  
  // Navigation & Layout
  Home,
  Grid,
  List,
  Calendar,
  Bell,
  Star,
  Heart,
  Bookmark,
  BookOpen,
  
  // AI & Technology
  Brain,
  Zap,
  Cpu,
  Database,
  Cloud,
  
  // Security & Privacy
  Lock,
  Unlock,
  Eye,
  EyeOff,
  Key,
  
  // Social & Community
  ThumbsUp,
  ThumbsDown,
  Flag,
  Award,
  Trophy,
  
  // Time & Scheduling
  Calendar as CalendarIcon,
  Clock as TimeIcon,
  Timer,
  
  // Media & Content
  Play,
  Pause,
  Stop,
  Volume2,
  VolumeX,
  
  // Utility
  MoreHorizontal,
  MoreVertical,
  Refresh,
  RotateCcw,
  ExternalLink,
  Link,
  
  // Status Indicators
  Wifi,
  WifiOff,
  Signal,
  Battery,
  
  // Forms & Input
  Send,
  Paperclip,
  AtSign,
  Hash,
  
  // Layout & Design
  Maximize,
  Minimize,
  Square,
  Circle,
  Triangle,
  
  // Development & Debug
  Code,
  Terminal,
  Bug,
  Wrench,
  
  // Specific Business Features
  Handshake, // For partnerships/mentorship
  Rocket,    // For startups/launch
  Globe,     // For global/international
  MapPin,    // For location
  Compass,   // For navigation/direction
  
} from 'lucide-react';

// Icon size constants for consistency
export const ICON_SIZES = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
  xxl: 48,
} as const;

// Common icon props for consistency
export const ICON_PROPS = {
  strokeWidth: 1.5,
  className: 'inline-block',
} as const;

// Utility function to get icon with consistent props
export const getIcon = (IconComponent: any, size: keyof typeof ICON_SIZES = 'md', className?: string) => {
  return {
    ...ICON_PROPS,
    size: ICON_SIZES[size],
    className: `${ICON_PROPS.className} ${className || ''}`.trim(),
  };
};

// Pre-configured icon sets for common use cases
export const DASHBOARD_ICONS = {
  analytics: BarChart3,
  users: Users,
  revenue: DollarSign,
  growth: TrendingUp,
  ideas: Lightbulb,
  settings: Settings,
} as const;

export const NAVIGATION_ICONS = {
  home: Home,
  dashboard: Grid,
  profile: User,
  settings: Settings,
  logout: ArrowLeft,
  menu: Menu,
  close: X,
} as const;

export const ACTION_ICONS = {
  create: Plus,
  edit: Edit,
  delete: Trash2,
  save: Save,
  cancel: X,
  confirm: Check,
  share: Share,
  download: Download,
} as const;

export const STATUS_ICONS = {
  success: CheckCircle,
  error: XCircle,
  warning: AlertTriangle,
  info: Info,
  loading: Clock,
} as const;

// Type definitions for better TypeScript support
export type IconSize = keyof typeof ICON_SIZES;
export type DashboardIcon = keyof typeof DASHBOARD_ICONS;
export type NavigationIcon = keyof typeof NAVIGATION_ICONS;
export type ActionIcon = keyof typeof ACTION_ICONS;
export type StatusIcon = keyof typeof STATUS_ICONS;
