import React, { useEffect, useState, memo, lazy, Suspense } from 'react';
import LazyErrorBoundary from './LazyErrorBoundary';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  navigationTime: number;
}

// Lazy load the performance dashboard only when needed
const PerformanceDashboard = lazy(() =>
  import('./PerformanceDashboard').catch(() => ({
    default: () => <div className="text-sm text-gray-500">Dashboard unavailable</div>
  }))
);

/**
 * Optimized Performance monitoring component
 * - Lazy loads dashboard components
 * - Reduces memory overhead
 * - Only renders in development mode
 */
const PerformanceMonitor: React.FC = memo(() => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isDashboardLoaded, setIsDashboardLoaded] = useState(false);

  useEffect(() => {
    // Only run in development
    if (process.env.NODE_ENV !== 'development') return;

    const measurePerformance = () => {
      try {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        const memory = (performance as any).memory;

        const loadTime = navigation.loadEventEnd - navigation.loadEventStart;
        const renderTime = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
        const memoryUsage = memory ? memory.usedJSHeapSize / 1024 / 1024 : 0;
        const navigationTime = navigation.loadEventEnd - navigation.fetchStart;

        setMetrics({
          loadTime: Math.round(loadTime),
          renderTime: Math.round(renderTime),
          memoryUsage: Math.round(memoryUsage * 100) / 100,
          navigationTime: Math.round(navigationTime)
        });

        // Optimized performance warnings - only log critical issues
        if (loadTime > 5000) {
          console.warn('🐌 Critical: Slow page load detected:', loadTime + 'ms');
        }
        if (memoryUsage > 100) {
          console.warn('🧠 Critical: High memory usage detected:', memoryUsage + 'MB');
        }
        if (navigationTime > 10000) {
          console.warn('🚀 Critical: Slow navigation detected:', navigationTime + 'ms');
        }

      } catch (error) {
        console.error('Performance monitoring error:', error);
      }
    };

    // Measure performance after page load
    if (document.readyState === 'complete') {
      measurePerformance();
    } else {
      window.addEventListener('load', measurePerformance);
    }

    return () => {
      window.removeEventListener('load', measurePerformance);
    };
  }, []);

  // Only show in development and when metrics are available
  if (process.env.NODE_ENV !== 'development' || !metrics) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => {
          setIsVisible(!isVisible);
          if (!isDashboardLoaded && !isVisible) {
            setIsDashboardLoaded(true);
          }
        }}
        className="bg-blue-600 text-white px-3 py-2 rounded-lg text-sm font-medium shadow-lg hover:bg-blue-700 transition-colors"
        title="Performance Metrics (Optimized)"
      >
        📊 Perf
      </button>

      {isVisible && (
        <div className="absolute bottom-12 right-0 bg-white border border-gray-200 rounded-lg shadow-xl p-4 min-w-[250px]">
          <h3 className="font-semibold text-gray-800 mb-3">Performance Metrics</h3>

          {isDashboardLoaded ? (
            <LazyErrorBoundary
              componentName="PerformanceDashboard"
              fallback={<div className="text-sm text-red-500">Dashboard error</div>}
            >
              <Suspense fallback={<div className="text-sm text-gray-500">Loading dashboard...</div>}>
                <PerformanceDashboard metrics={metrics} />
              </Suspense>
            </LazyErrorBoundary>
          ) : (
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Load Time:</span>
                <span className={metrics.loadTime > 5000 ? 'text-red-600 font-semibold' : 'text-green-600'}>
                  {metrics.loadTime}ms
                </span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Memory Usage:</span>
                <span className={metrics.memoryUsage > 100 ? 'text-red-600 font-semibold' : 'text-green-600'}>
                  {metrics.memoryUsage}MB
                </span>
              </div>

              <button
                onClick={() => setIsDashboardLoaded(true)}
                className="w-full mt-2 text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded"
              >
                Load Full Dashboard
              </button>
            </div>
          )}

          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="text-xs text-gray-500">
              🟢 Optimized for Performance
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

PerformanceMonitor.displayName = 'PerformanceMonitor';

export default PerformanceMonitor;
