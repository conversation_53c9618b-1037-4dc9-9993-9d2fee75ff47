/**
 * Role-Based AI Access Control Component
 * Controls access to AI features based on user roles and capabilities
 */

import React, { ReactNode } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { useTranslation } from 'react-i18next';
import { Shield, Lock, Crown, Zap, AlertTriangle } from 'lucide-react';
import { getUserRoles, hasRole, UserRole } from '../../utils/unifiedRoleManager';
import { getUserAICapabilities, AICapability as AICapabilityInterface } from '../../utils/roleBasedAI';

// Define capability types for this component
export type AICapability = 'basic_chat' | 'advanced_chat' | 'basic_analysis' | 'advanced_analysis' |
  'business_plan_generation' | 'market_research' | 'financial_modeling' | 'predictive_analytics' |
  'voice_ai' | 'computer_vision' | 'system_monitoring' | 'ai_configuration' | 'unlimited_access';

interface RoleBasedAIAccessProps {
  children: ReactNode;
  requiredCapability?: AICapability;
  requiredRole?: 'user' | 'admin' | 'mentor' | 'investor' | 'super_admin';
  showUpgradePrompt?: boolean;
  fallbackComponent?: ReactNode;
  className?: string;
}

interface AICapabilitiesDisplayProps {
  showHeader?: boolean;
  compact?: boolean;
  className?: string;
}

/**
 * Main Role-Based Access Control Component
 */
const RoleBasedAIAccess: React.FC<RoleBasedAIAccessProps> = ({
  children,
  requiredCapability,
  requiredRole,
  showUpgradePrompt = false,
  fallbackComponent,
  className = ''
}) => {
  const { t } = useTranslation();
  const { user } = useSelector((state: RootState) => state.auth);

  // Check if user is authenticated
  if (!user) {
    return (
      <div className={`glass-light rounded-xl p-8 border border-glass-border text-center ${className}`}>
        <Lock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-white mb-2">
          {t('ai.access.loginRequired', 'Login Required')}
        </h3>
        <p className="text-gray-300">
          {t('ai.access.loginMessage', 'Please log in to access AI features.')}
        </p>
      </div>
    );
  }

  const userRoles = getUserRoles(user);
  const userCapabilities = getUserAICapabilities(user);
  const userCapabilityIds = userCapabilities.map(cap => cap.id);

  // ✅ SECURITY FIX: Use unified role manager instead of custom logic
  if (requiredRole) {
    const hasRequiredRole = hasRole(user, requiredRole);

    if (!hasRequiredRole) {
      return (
        <div className={`glass-light rounded-xl p-8 border border-red-500/20 text-center ${className}`}>
          <Shield className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">
            {t('ai.access.insufficientRole', 'Insufficient Permissions')}
          </h3>
          <p className="text-gray-300 mb-4">
            {t('ai.access.roleRequired', `This feature requires ${requiredRole} access.`)}
          </p>
          {showUpgradePrompt && (
            <button className="btn-primary">
              {t('ai.access.requestAccess', 'Request Access')}
            </button>
          )}
        </div>
      );
    }
  }

  // Check capability-based access
  if (requiredCapability && !userCapabilityIds.includes(requiredCapability)) {
    if (fallbackComponent) {
      return <>{fallbackComponent}</>;
    }

    return (
      <div className={`glass-light rounded-xl p-8 border border-yellow-500/20 text-center ${className}`}>
        <Zap className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-white mb-2">
          {t('ai.access.capabilityRequired', 'Feature Not Available')}
        </h3>
        <p className="text-gray-300 mb-4">
          {t('ai.access.capabilityMessage', `This feature requires ${requiredCapability} capability.`)}
        </p>
        {showUpgradePrompt && (
          <div className="space-y-3">
            <button className="btn-primary">
              {t('ai.access.upgrade', 'Upgrade Access')}
            </button>
            <p className="text-sm text-gray-400">
              {t('ai.access.upgradeMessage', 'Contact your administrator to upgrade your AI access.')}
            </p>
          </div>
        )}
      </div>
    );
  }

  // User has access - render children
  return <div className={className}>{children}</div>;
};

/**
 * AI Capabilities Display Component
 */
export const AICapabilitiesDisplay: React.FC<AICapabilitiesDisplayProps> = ({
  showHeader = true,
  compact = false,
  className = ''
}) => {
  const { t } = useTranslation();
  const { user } = useSelector((state: RootState) => state.auth);

  if (!user) return null;

  const userRoles = getUserRoles(user);
  const userCapabilities = getUserAICapabilities(user);

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'super_admin':
        return <Crown className="w-4 h-4 text-yellow-400" />;
      case 'admin':
        return <Shield className="w-4 h-4 text-blue-400" />;
      case 'mentor':
        return <Zap className="w-4 h-4 text-green-400" />;
      case 'investor':
        return <AlertTriangle className="w-4 h-4 text-purple-400" />;
      default:
        return <Shield className="w-4 h-4 text-gray-400" />;
    }
  };

  const getCapabilityLabel = (capability: AICapability): string => {
    const labels: Record<AICapability, string> = {
      basic_chat: t('ai.capabilities.basicChat', 'Basic Chat'),
      advanced_chat: t('ai.capabilities.advancedChat', 'Advanced Chat'),
      basic_analysis: t('ai.capabilities.basicAnalysis', 'Basic Analysis'),
      advanced_analysis: t('ai.capabilities.advancedAnalysis', 'Advanced Analysis'),
      business_plan_generation: t('ai.capabilities.businessPlanGeneration', 'Business Plan Generation'),
      market_research: t('ai.capabilities.marketResearch', 'Market Research'),
      financial_modeling: t('ai.capabilities.financialModeling', 'Financial Modeling'),
      predictive_analytics: t('ai.capabilities.predictiveAnalytics', 'Predictive Analytics'),
      voice_ai: t('ai.capabilities.voiceAI', 'Voice AI'),
      computer_vision: t('ai.capabilities.computerVision', 'Computer Vision'),
      system_monitoring: t('ai.capabilities.systemMonitoring', 'System Monitoring'),
      ai_configuration: t('ai.capabilities.aiConfiguration', 'AI Configuration'),
      unlimited_access: t('ai.capabilities.unlimitedAccess', 'Unlimited Access')
    };
    return labels[capability] || capability;
  };

  if (compact) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="flex items-center space-x-1">
          {userRoles.map((role) => (
            <div key={role} className="flex items-center">
              {getRoleIcon(role)}
            </div>
          ))}
        </div>
        <span className="text-sm text-gray-300">
          {userCapabilities.length} {t('ai.capabilities.available', 'capabilities')}
        </span>
      </div>
    );
  }

  return (
    <div className={`glass-light rounded-xl p-6 border border-glass-border ${className}`}>
      {showHeader && (
        <h3 className="text-lg font-semibold text-white mb-4">
          {t('ai.access.yourAccess', 'Your AI Access')}
        </h3>
      )}
      
      {/* User Roles */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-300 mb-2">
          {t('ai.access.roles', 'Roles')}
        </h4>
        <div className="flex flex-wrap gap-2">
          {userRoles.map((role) => (
            <div
              key={role}
              className="flex items-center space-x-2 bg-glass-light px-3 py-1 rounded-lg border border-glass-border"
            >
              {getRoleIcon(role)}
              <span className="text-sm text-white capitalize">
                {role.replace('_', ' ')}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* AI Capabilities */}
      <div>
        <h4 className="text-sm font-medium text-gray-300 mb-2">
          {t('ai.access.capabilities', 'AI Capabilities')}
        </h4>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          {userCapabilities.map((capability) => (
            <div
              key={capability.id}
              className="flex items-center space-x-2 bg-glass-light px-3 py-2 rounded-lg border border-glass-border"
            >
              <Zap className="w-3 h-3 text-green-400" />
              <span className="text-sm text-white">
                {capability.name}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RoleBasedAIAccess;
