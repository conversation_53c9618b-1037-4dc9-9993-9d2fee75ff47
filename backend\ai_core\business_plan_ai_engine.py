"""
AI-Powered Business Plan Generator
Extends existing backend AI infrastructure for intelligent business plan creation
Integrates with existing advanced_business_plan_service.py
"""

import logging
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from django.contrib.auth.models import User
from core.ai_service import ai_generate_intelligent_content, ai_is_available
from core.ai_config import get_gemini_config
from ai_recommendations.advanced_business_plan_service import get_configured_model
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

logger = logging.getLogger(__name__)


class BusinessPlanAIEngine:
    """
    AI-powered business plan generation engine
    Extends existing backend AI infrastructure and integrates with business plan service
    """
    
    def __init__(self):
        self.ai_config = get_gemini_config()
        self.is_available = ai_is_available()
        self.gemini_model = get_configured_model()
        
        # MENA market intelligence data
        self.mena_market_data = self._load_mena_market_data()
    
    def generate_comprehensive_business_plan(
        self,
        business_input: Dict[str, Any],
        user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Generate comprehensive business plan using AI
        Integrates with existing business plan infrastructure
        """
        try:
            if not self.is_available:
                return {
                    'success': False,
                    'error': 'AI service not available',
                    'business_plan': {}
                }
            
            # Prepare context for AI analysis
            context = {
                'business_input': business_input,
                'analysis_type': 'comprehensive_business_plan',
                'mena_context': self._get_mena_context(business_input.get('region', 'uae')),
                'industry_data': self._get_industry_data(business_input.get('industry', 'other')),
                'user_id': user_id,
                'timestamp': datetime.now().isoformat()
            }
            
            # Generate each section of the business plan
            sections = {}
            
            # 1. Executive Summary
            sections['executive_summary'] = self._generate_executive_summary(context)
            
            # 2. Market Analysis
            sections['market_analysis'] = self._generate_market_analysis(context)
            
            # 3. Business Model
            sections['business_model'] = self._generate_business_model(context)
            
            # 4. Financial Projections
            sections['financial_projections'] = self._generate_financial_projections(context)
            
            # 5. Marketing Strategy
            sections['marketing_strategy'] = self._generate_marketing_strategy(context)
            
            # 6. Operational Plan
            sections['operational_plan'] = self._generate_operational_plan(context)
            
            # 7. Risk Analysis
            sections['risk_analysis'] = self._generate_risk_analysis(context)
            
            # Compile complete business plan
            business_plan = {
                'metadata': {
                    'generated_at': datetime.now().isoformat(),
                    'business_idea': business_input.get('business_idea', ''),
                    'industry': business_input.get('industry', ''),
                    'region': business_input.get('region', ''),
                    'language': business_input.get('language', 'en')
                },
                'sections': sections,
                'ai_confidence': self._calculate_plan_confidence(sections),
                'recommendations': self._generate_plan_recommendations(sections)
            }
            
            return {
                'success': True,
                'business_plan': business_plan,
                'generation_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating business plan: {e}")
            return {
                'success': False,
                'error': str(e),
                'business_plan': {}
            }
    
    def _generate_executive_summary(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate AI-powered executive summary"""
        try:
            summary_context = {
                **context,
                'section_type': 'executive_summary',
                'focus': 'high_level_overview_and_value_proposition'
            }
            
            response = ai_generate_intelligent_content(
                'business_plan_executive_summary',
                summary_context,
                context['business_input'].get('language', 'en'),
                context.get('user_id')
            )
            
            if response.get('success'):
                return {
                    'content': response.get('data', {}).get('content', ''),
                    'key_points': self._extract_key_points(response.get('data', {}).get('content', '')),
                    'ai_confidence': response.get('data', {}).get('confidence', 0.8)
                }
            else:
                return {'content': '', 'key_points': [], 'ai_confidence': 0}
                
        except Exception as e:
            logger.error(f"Error generating executive summary: {e}")
            return {'content': '', 'key_points': [], 'ai_confidence': 0}
    
    def _generate_market_analysis(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate AI-powered market analysis with MENA focus"""
        try:
            market_context = {
                **context,
                'section_type': 'market_analysis',
                'focus': 'mena_market_sizing_and_competition',
                'regional_data': context.get('mena_context', {}),
                'industry_insights': context.get('industry_data', {})
            }
            
            response = ai_generate_intelligent_content(
                'business_plan_market_analysis',
                market_context,
                context['business_input'].get('language', 'en'),
                context.get('user_id')
            )
            
            if response.get('success'):
                content = response.get('data', {}).get('content', '')
                return {
                    'content': content,
                    'market_size': self._extract_market_size(content),
                    'competitors': self._extract_competitors(content),
                    'opportunities': self._extract_opportunities(content),
                    'threats': self._extract_threats(content),
                    'ai_confidence': response.get('data', {}).get('confidence', 0.8)
                }
            else:
                return {
                    'content': '',
                    'market_size': {},
                    'competitors': [],
                    'opportunities': [],
                    'threats': [],
                    'ai_confidence': 0
                }
                
        except Exception as e:
            logger.error(f"Error generating market analysis: {e}")
            return {
                'content': '',
                'market_size': {},
                'competitors': [],
                'opportunities': [],
                'threats': [],
                'ai_confidence': 0
            }
    
    def _generate_business_model(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate AI-powered business model"""
        try:
            model_context = {
                **context,
                'section_type': 'business_model',
                'focus': 'revenue_streams_and_value_proposition',
                'cultural_considerations': self._get_cultural_considerations(context['business_input'].get('region'))
            }
            
            response = ai_generate_intelligent_content(
                'business_plan_business_model',
                model_context,
                context['business_input'].get('language', 'en'),
                context.get('user_id')
            )
            
            if response.get('success'):
                content = response.get('data', {}).get('content', '')
                return {
                    'content': content,
                    'value_proposition': self._extract_value_proposition(content),
                    'revenue_streams': self._extract_revenue_streams(content),
                    'cost_structure': self._extract_cost_structure(content),
                    'key_partnerships': self._extract_key_partnerships(content),
                    'ai_confidence': response.get('data', {}).get('confidence', 0.8)
                }
            else:
                return {
                    'content': '',
                    'value_proposition': '',
                    'revenue_streams': [],
                    'cost_structure': [],
                    'key_partnerships': [],
                    'ai_confidence': 0
                }
                
        except Exception as e:
            logger.error(f"Error generating business model: {e}")
            return {
                'content': '',
                'value_proposition': '',
                'revenue_streams': [],
                'cost_structure': [],
                'key_partnerships': [],
                'ai_confidence': 0
            }
    
    def _generate_financial_projections(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate AI-powered financial projections"""
        try:
            financial_context = {
                **context,
                'section_type': 'financial_projections',
                'focus': 'revenue_expenses_and_funding_requirements',
                'timeframe': context['business_input'].get('timeframe', 36),  # months
                'funding_goal': context['business_input'].get('funding_goal', 100000)
            }
            
            response = ai_generate_intelligent_content(
                'business_plan_financial_projections',
                financial_context,
                context['business_input'].get('language', 'en'),
                context.get('user_id')
            )
            
            if response.get('success'):
                content = response.get('data', {}).get('content', '')
                return {
                    'content': content,
                    'revenue_projections': self._extract_revenue_projections(content),
                    'expense_projections': self._extract_expense_projections(content),
                    'funding_requirements': self._extract_funding_requirements(content),
                    'break_even_analysis': self._extract_break_even_analysis(content),
                    'ai_confidence': response.get('data', {}).get('confidence', 0.8)
                }
            else:
                return {
                    'content': '',
                    'revenue_projections': [],
                    'expense_projections': [],
                    'funding_requirements': [],
                    'break_even_analysis': {},
                    'ai_confidence': 0
                }
                
        except Exception as e:
            logger.error(f"Error generating financial projections: {e}")
            return {
                'content': '',
                'revenue_projections': [],
                'expense_projections': [],
                'funding_requirements': [],
                'break_even_analysis': {},
                'ai_confidence': 0
            }
    
    def _generate_marketing_strategy(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate AI-powered marketing strategy with Arabic/MENA focus"""
        try:
            marketing_context = {
                **context,
                'section_type': 'marketing_strategy',
                'focus': 'arabic_digital_marketing_and_cultural_adaptation',
                'target_demographics': self._get_target_demographics(context['business_input'].get('region')),
                'digital_channels': self._get_digital_channels_mena()
            }
            
            response = ai_generate_intelligent_content(
                'business_plan_marketing_strategy',
                marketing_context,
                context['business_input'].get('language', 'en'),
                context.get('user_id')
            )
            
            if response.get('success'):
                content = response.get('data', {}).get('content', '')
                return {
                    'content': content,
                    'target_audience': self._extract_target_audience(content),
                    'marketing_channels': self._extract_marketing_channels(content),
                    'budget_allocation': self._extract_budget_allocation(content),
                    'cultural_adaptations': self._extract_cultural_adaptations(content),
                    'ai_confidence': response.get('data', {}).get('confidence', 0.8)
                }
            else:
                return {
                    'content': '',
                    'target_audience': {},
                    'marketing_channels': [],
                    'budget_allocation': {},
                    'cultural_adaptations': [],
                    'ai_confidence': 0
                }
                
        except Exception as e:
            logger.error(f"Error generating marketing strategy: {e}")
            return {
                'content': '',
                'target_audience': {},
                'marketing_channels': [],
                'budget_allocation': {},
                'cultural_adaptations': [],
                'ai_confidence': 0
            }
    
    def _generate_operational_plan(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate AI-powered operational plan"""
        # Implementation for operational plan generation
        return {'content': '', 'milestones': [], 'team_requirements': [], 'ai_confidence': 0}
    
    def _generate_risk_analysis(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate AI-powered risk analysis"""
        # Implementation for risk analysis generation
        return {'content': '', 'risks': [], 'mitigation_strategies': [], 'ai_confidence': 0}
    
    def _load_mena_market_data(self) -> Dict[str, Any]:
        """Load MENA market intelligence data"""
        return {
            'saudi': {
                'market_size': {'fintech': 2.4e9, 'healthtech': 1.8e9, 'edtech': 1.2e9},
                'population': 35000000,
                'gdp_per_capita': 23000,
                'internet_penetration': 0.98,
                'mobile_penetration': 1.2,
                'key_cities': ['Riyadh', 'Jeddah', 'Dammam'],
                'business_culture': 'conservative_traditional',
                'regulations': ['SAMA', 'SFDA', 'MOE']
            },
            'uae': {
                'market_size': {'fintech': 3.2e9, 'healthtech': 2.1e9, 'edtech': 1.5e9},
                'population': 10000000,
                'gdp_per_capita': 43000,
                'internet_penetration': 0.99,
                'mobile_penetration': 2.1,
                'key_cities': ['Dubai', 'Abu Dhabi', 'Sharjah'],
                'business_culture': 'international_business_friendly',
                'regulations': ['CBUAE', 'DHA', 'KHDA']
            },
            'egypt': {
                'market_size': {'fintech': 1.8e9, 'healthtech': 1.2e9, 'edtech': 0.8e9},
                'population': 104000000,
                'gdp_per_capita': 3500,
                'internet_penetration': 0.72,
                'mobile_penetration': 0.95,
                'key_cities': ['Cairo', 'Alexandria', 'Giza'],
                'business_culture': 'price_sensitive_large_market',
                'regulations': ['CBE', 'MOH', 'MOE']
            }
        }
    
    def _get_mena_context(self, region: str) -> Dict[str, Any]:
        """Get MENA-specific context for the region"""
        return self.mena_market_data.get(region, self.mena_market_data['uae'])
    
    def _get_industry_data(self, industry: str) -> Dict[str, Any]:
        """Get industry-specific data and insights"""
        industry_data = {
            'fintech': {
                'growth_rate': 0.25,
                'key_trends': ['digital_payments', 'islamic_finance', 'blockchain'],
                'regulatory_focus': 'high',
                'investment_activity': 'very_high'
            },
            'healthtech': {
                'growth_rate': 0.18,
                'key_trends': ['telemedicine', 'ai_diagnostics', 'health_apps'],
                'regulatory_focus': 'very_high',
                'investment_activity': 'high'
            },
            'edtech': {
                'growth_rate': 0.22,
                'key_trends': ['online_learning', 'arabic_content', 'mobile_first'],
                'regulatory_focus': 'medium',
                'investment_activity': 'medium'
            }
        }
        
        return industry_data.get(industry, industry_data['fintech'])
    
    # Helper methods for content extraction
    def _extract_key_points(self, content: str) -> List[str]:
        """Extract key points from AI-generated content"""
        # Implementation for extracting key points
        return []
    
    def _extract_market_size(self, content: str) -> Dict[str, Any]:
        """Extract market size information"""
        return {}
    
    def _extract_competitors(self, content: str) -> List[Dict[str, Any]]:
        """Extract competitor information"""
        return []
    
    def _extract_opportunities(self, content: str) -> List[str]:
        """Extract market opportunities"""
        return []
    
    def _extract_threats(self, content: str) -> List[str]:
        """Extract market threats"""
        return []
    
    def _extract_value_proposition(self, content: str) -> str:
        """Extract value proposition"""
        return ""
    
    def _extract_revenue_streams(self, content: str) -> List[Dict[str, Any]]:
        """Extract revenue streams"""
        return []
    
    def _extract_cost_structure(self, content: str) -> List[Dict[str, Any]]:
        """Extract cost structure"""
        return []
    
    def _extract_key_partnerships(self, content: str) -> List[str]:
        """Extract key partnerships"""
        return []
    
    def _calculate_plan_confidence(self, sections: Dict[str, Any]) -> float:
        """Calculate overall business plan confidence score"""
        confidences = []
        for section in sections.values():
            if isinstance(section, dict) and 'ai_confidence' in section:
                confidences.append(section['ai_confidence'])
        
        return sum(confidences) / len(confidences) if confidences else 0.0
    
    def _generate_plan_recommendations(self, sections: Dict[str, Any]) -> List[str]:
        """Generate recommendations for improving the business plan"""
        recommendations = []
        
        # Analyze each section and provide recommendations
        for section_name, section_data in sections.items():
            if isinstance(section_data, dict):
                confidence = section_data.get('ai_confidence', 0)
                if confidence < 0.7:
                    recommendations.append(f"Consider strengthening the {section_name.replace('_', ' ')} section")
        
        return recommendations


# Global service instance
_business_plan_ai_engine = None


def get_business_plan_ai_engine() -> BusinessPlanAIEngine:
    """Get the global business plan AI engine instance"""
    global _business_plan_ai_engine
    if _business_plan_ai_engine is None:
        _business_plan_ai_engine = BusinessPlanAIEngine()
    return _business_plan_ai_engine


# ========================================
# API VIEWS FOR BUSINESS PLAN AI
# ========================================

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def generate_business_plan_api(request):
    """
    API endpoint for AI-powered business plan generation
    """
    try:
        business_input = request.data.get('business_input', {})

        if not business_input:
            return Response({
                'success': False,
                'error': 'business_input is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate required fields
        required_fields = ['business_idea', 'industry', 'region']
        missing_fields = [field for field in required_fields if not business_input.get(field)]

        if missing_fields:
            return Response({
                'success': False,
                'error': f'Missing required fields: {", ".join(missing_fields)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        engine = get_business_plan_ai_engine()
        result = engine.generate_comprehensive_business_plan(business_input, request.user.id)

        return Response(result, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error in generate_business_plan_api: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def analyze_business_plan_section_api(request):
    """
    API endpoint for analyzing specific business plan sections
    """
    try:
        section_type = request.data.get('section_type', '')
        business_input = request.data.get('business_input', {})

        if not section_type or not business_input:
            return Response({
                'success': False,
                'error': 'section_type and business_input are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        engine = get_business_plan_ai_engine()

        # Generate specific section based on type
        context = {
            'business_input': business_input,
            'analysis_type': f'business_plan_{section_type}',
            'mena_context': engine._get_mena_context(business_input.get('region', 'uae')),
            'industry_data': engine._get_industry_data(business_input.get('industry', 'other')),
            'user_id': request.user.id,
            'timestamp': datetime.now().isoformat()
        }

        if section_type == 'executive_summary':
            result = engine._generate_executive_summary(context)
        elif section_type == 'market_analysis':
            result = engine._generate_market_analysis(context)
        elif section_type == 'business_model':
            result = engine._generate_business_model(context)
        elif section_type == 'financial_projections':
            result = engine._generate_financial_projections(context)
        elif section_type == 'marketing_strategy':
            result = engine._generate_marketing_strategy(context)
        elif section_type == 'operational_plan':
            result = engine._generate_operational_plan(context)
        elif section_type == 'risk_analysis':
            result = engine._generate_risk_analysis(context)
        else:
            return Response({
                'success': False,
                'error': f'Unknown section_type: {section_type}'
            }, status=status.HTTP_400_BAD_REQUEST)

        return Response({
            'success': True,
            'section_type': section_type,
            'section_data': result
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error in analyze_business_plan_section_api: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_mena_market_data_api(request):
    """
    API endpoint for getting MENA market intelligence data
    """
    try:
        region = request.GET.get('region', 'all')
        industry = request.GET.get('industry', 'all')

        engine = get_business_plan_ai_engine()

        if region == 'all':
            market_data = engine.mena_market_data
        else:
            market_data = {region: engine._get_mena_context(region)}

        if industry != 'all':
            # Filter by industry if specified
            for region_key, region_data in market_data.items():
                if 'market_size' in region_data and industry in region_data['market_size']:
                    market_data[region_key]['market_size'] = {
                        industry: region_data['market_size'][industry]
                    }

        return Response({
            'success': True,
            'market_data': market_data,
            'timestamp': datetime.now().isoformat()
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error in get_mena_market_data_api: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
