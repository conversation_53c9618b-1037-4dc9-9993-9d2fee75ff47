/**
 * Error Tracking and Monitoring Service
 * Provides comprehensive error tracking, logging, and monitoring capabilities
 */

interface ErrorContext {
  userId?: string;
  userRole?: string;
  component?: string;
  action?: string;
  apiEndpoint?: string;
  timestamp: string;
  userAgent: string;
  url: string;
  additionalData?: Record<string, any>;
}

interface APIError {
  endpoint: string;
  method: string;
  status: number;
  statusText: string;
  responseData?: any;
  requestData?: any;
  duration: number;
}

interface UserError {
  type: 'user_action' | 'ui_error' | 'validation_error';
  message: string;
  component: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

class ErrorTrackingService {
  private errors: Array<ErrorContext & (APIError | UserError)> = [];
  private maxErrors = 1000; // Keep last 1000 errors in memory
  private isEnabled = true;

  constructor() {
    this.setupGlobalErrorHandlers();
  }

  /**
   * Set up global error handlers for unhandled errors
   */
  private setupGlobalErrorHandlers(): void {
    // Handle unhandled JavaScript errors
    window.addEventListener('error', (event) => {
      this.trackError({
        type: 'javascript_error',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        severity: 'high'
      });
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.trackError({
        type: 'unhandled_promise_rejection',
        message: event.reason?.message || 'Unhandled promise rejection',
        reason: event.reason,
        severity: 'high'
      });
    });

    // Handle React error boundaries (if using)
    if (typeof window !== 'undefined' && (window as any).__REACT_ERROR_OVERLAY_GLOBAL_HOOK__) {
      console.log('✅ React Error Overlay detected - errors will be tracked');
    }
  }

  /**
   * Track API errors with detailed context
   */
  trackAPIError(error: APIError, context?: Partial<ErrorContext>): void {
    if (!this.isEnabled) return;

    const errorData = {
      ...this.getBaseContext(),
      ...context,
      ...error,
      type: 'api_error',
      severity: this.getAPISeverity(error.status)
    };

    this.addError(errorData);
    this.logError('API Error', errorData);

    // Send to external monitoring service if configured
    this.sendToMonitoringService(errorData);
  }

  /**
   * Track user interaction errors
   */
  trackUserError(error: UserError, context?: Partial<ErrorContext>): void {
    if (!this.isEnabled) return;

    const errorData = {
      ...this.getBaseContext(),
      ...context,
      ...error,
      type: 'user_error'
    };

    this.addError(errorData);
    this.logError('User Error', errorData);

    // Only send high/critical user errors to monitoring
    if (error.severity === 'high' || error.severity === 'critical') {
      this.sendToMonitoringService(errorData);
    }
  }

  /**
   * Track general errors with custom context
   */
  trackError(error: any, context?: Partial<ErrorContext>): void {
    if (!this.isEnabled) return;

    const errorData = {
      ...this.getBaseContext(),
      ...context,
      message: error.message || 'Unknown error',
      stack: error.stack,
      type: error.type || 'general_error',
      severity: error.severity || 'medium'
    };

    this.addError(errorData);
    this.logError('General Error', errorData);
    this.sendToMonitoringService(errorData);
  }

  /**
   * Track performance issues
   */
  trackPerformanceIssue(metric: string, value: number, threshold: number, context?: Partial<ErrorContext>): void {
    if (!this.isEnabled || value <= threshold) return;

    const errorData = {
      ...this.getBaseContext(),
      ...context,
      type: 'performance_issue',
      metric,
      value,
      threshold,
      severity: value > threshold * 2 ? 'high' : 'medium',
      message: `Performance issue: ${metric} (${value}) exceeded threshold (${threshold})`
    };

    this.addError(errorData);
    this.logError('Performance Issue', errorData);
  }

  /**
   * Get base context information
   */
  private getBaseContext(): ErrorContext {
    return {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.getCurrentUserId(),
      userRole: this.getCurrentUserRole()
    };
  }

  /**
   * Get current user ID from localStorage or Redux store
   */
  private getCurrentUserId(): string | undefined {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      return user.id?.toString();
    } catch {
      return undefined;
    }
  }

  /**
   * Get current user role
   */
  private getCurrentUserRole(): string | undefined {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      return user.profile?.roles?.[0] || user.role;
    } catch {
      return undefined;
    }
  }

  /**
   * Determine API error severity based on status code
   */
  private getAPISeverity(status: number): 'low' | 'medium' | 'high' | 'critical' {
    if (status >= 500) return 'critical';
    if (status >= 400) return 'high';
    if (status >= 300) return 'medium';
    return 'low';
  }

  /**
   * Add error to internal storage
   */
  private addError(error: any): void {
    this.errors.unshift(error);
    
    // Keep only the most recent errors
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors);
    }
  }

  /**
   * Log error to console with appropriate level
   */
  private logError(type: string, error: any): void {
    const logLevel = error.severity === 'critical' ? 'error' : 
                    error.severity === 'high' ? 'error' :
                    error.severity === 'medium' ? 'warn' : 'info';

    console[logLevel](`[${type}]`, {
      message: error.message,
      endpoint: error.endpoint,
      component: error.component,
      timestamp: error.timestamp,
      ...error
    });
  }

  /**
   * Send error to external monitoring service
   */
  private sendToMonitoringService(error: any): void {
    // In a real implementation, this would send to services like:
    // - Sentry
    // - LogRocket
    // - Datadog
    // - Custom logging endpoint

    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Would send to monitoring service:', error);
      return;
    }

    // Example implementation for custom logging endpoint
    try {
      fetch('/api/logging/errors/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(error)
      }).catch(err => {
        console.warn('Failed to send error to monitoring service:', err);
      });
    } catch (err) {
      console.warn('Error tracking service failed:', err);
    }
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    total: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
    recent: any[];
  } {
    const byType: Record<string, number> = {};
    const bySeverity: Record<string, number> = {};

    this.errors.forEach(error => {
      byType[error.type] = (byType[error.type] || 0) + 1;
      bySeverity[error.severity] = (bySeverity[error.severity] || 0) + 1;
    });

    return {
      total: this.errors.length,
      byType,
      bySeverity,
      recent: this.errors.slice(0, 10) // Last 10 errors
    };
  }

  /**
   * Clear all stored errors
   */
  clearErrors(): void {
    this.errors = [];
  }

  /**
   * Enable/disable error tracking
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * Export errors for debugging
   */
  exportErrors(): string {
    return JSON.stringify(this.errors, null, 2);
  }
}

// Create singleton instance
export const errorTracker = new ErrorTrackingService();

// Helper functions for common error tracking scenarios
export const trackAPIError = (error: APIError, context?: Partial<ErrorContext>) => {
  errorTracker.trackAPIError(error, context);
};

export const trackUserError = (error: UserError, context?: Partial<ErrorContext>) => {
  errorTracker.trackUserError(error, context);
};

export const trackComponentError = (component: string, error: any, action?: string) => {
  errorTracker.trackError(error, { component, action });
};

export const trackPerformanceIssue = (metric: string, value: number, threshold: number, context?: Partial<ErrorContext>) => {
  errorTracker.trackPerformanceIssue(metric, value, threshold, context);
};

export default errorTracker;
