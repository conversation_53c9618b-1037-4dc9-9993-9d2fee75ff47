"""
AI Core URLs
Enhanced AI endpoints for Phase 1 differentiation features
"""

from django.urls import path
from .mentorship_ai_engine import (
    find_mentors_api,
    predict_mentorship_success_api,
    optimize_mentorship_schedule_api,
    generate_mentorship_plan_api
)
from .business_plan_ai_engine import (
    generate_business_plan_api,
    analyze_business_plan_section_api,
    get_mena_market_data_api
)

app_name = 'ai_core'

urlpatterns = [
    # ========================================
    # AI MENTORSHIP ENDPOINTS
    # ========================================
    
    # AI-powered mentor matching
    path('mentorship/find-mentors/', find_mentors_api, name='find_mentors'),
    
    # Mentorship success prediction
    path('mentorship/predict-success/', predict_mentorship_success_api, name='predict_mentorship_success'),
    
    # Schedule optimization
    path('mentorship/optimize-schedule/', optimize_mentorship_schedule_api, name='optimize_mentorship_schedule'),
    
    # Mentorship plan generation
    path('mentorship/generate-plan/', generate_mentorship_plan_api, name='generate_mentorship_plan'),
    
    # ========================================
    # AI BUSINESS PLAN ENDPOINTS
    # ========================================
    
    # Comprehensive business plan generation
    path('business-plan/generate/', generate_business_plan_api, name='generate_business_plan'),
    
    # Specific section analysis
    path('business-plan/analyze-section/', analyze_business_plan_section_api, name='analyze_business_plan_section'),
    
    # MENA market intelligence
    path('business-plan/mena-market-data/', get_mena_market_data_api, name='get_mena_market_data'),
]
