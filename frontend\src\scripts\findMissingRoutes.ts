/**
 * FIND MISSING ROUTES SCRIPT
 * Identifies routes that are in consolidatedRoutes.ts but missing from centralizedRoleRouteMapping.ts
 */

import { allRoutes } from '../routes/consolidatedRoutes';
import { ROUTE_ROLE_MAPPINGS } from '../config/centralizedRoleRouteMapping';

function findMissingRoutes() {
  console.log('🔍 Finding missing routes...\n');

  // Get all route paths from consolidated routes
  const consolidatedRoutePaths = allRoutes.map(route => route.path);
  
  // Get all route paths from centralized mapping
  const centralizedRoutePaths = ROUTE_ROLE_MAPPINGS.map(route => route.path);
  
  // Find routes that are in consolidated but not in centralized
  const missingRoutes = consolidatedRoutePaths.filter(path => 
    !centralizedRoutePaths.includes(path)
  );
  
  // Find routes that are in centralized but not in consolidated
  const extraRoutes = centralizedRoutePaths.filter(path => 
    !consolidatedRoutePaths.includes(path) && 
    !path.startsWith('/login') && 
    !path.startsWith('/register') && 
    path !== '/'
  );

  console.log('📊 ROUTE ANALYSIS:');
  console.log(`Total consolidated routes: ${consolidatedRoutePaths.length}`);
  console.log(`Total centralized routes: ${centralizedRoutePaths.length}`);
  console.log(`Missing from centralized: ${missingRoutes.length}`);
  console.log(`Extra in centralized: ${extraRoutes.length}\n`);

  if (missingRoutes.length > 0) {
    console.log('❌ ROUTES MISSING FROM CENTRALIZED MAPPING:');
    missingRoutes.forEach(route => {
      const routeConfig = allRoutes.find(r => r.path === route);
      console.log(`  - ${route} (${routeConfig?.loadingMessage || 'No loading message'})`);
    });
    console.log('');
  }

  if (extraRoutes.length > 0) {
    console.log('➕ EXTRA ROUTES IN CENTRALIZED MAPPING:');
    extraRoutes.forEach(route => {
      console.log(`  - ${route}`);
    });
    console.log('');
  }

  if (missingRoutes.length === 0 && extraRoutes.length === 0) {
    console.log('✅ All routes are properly synchronized!');
  }

  return {
    missingRoutes,
    extraRoutes,
    consolidatedRoutePaths,
    centralizedRoutePaths
  };
}

// Run the script
if (typeof window === 'undefined') {
  findMissingRoutes();
}

export { findMissingRoutes };
